<script>
	import { initRouterInterceptor } from './common/permission-router.js';
	import { initH5RouterGuard } from './common/h5-router-guard.js';
	import permissionService from './common/permission-service.js';
	import { getPermissionsFromCache } from './pages/user/api/permission.js';

	export default {
		data() {
			return {
				// 权限检查定时器
				permissionCheckTimer: null
			}
		},
		onLaunch: function() {
			console.log('App Launch');

			// 初始化路由拦截器（必须最先初始化，确保所有路由操作都被拦截）
			initRouterInterceptor();

			// 初始化H5路由守卫（仅在H5环境下生效）
			// #ifdef H5
			console.log('App Launch - 初始化H5路由守卫');
			initH5RouterGuard();
			// #endif

			// 检查登录状态
			const token = uni.getStorageSync('token');
			if (token) {
				// 如果已登录，立即加载权限
				console.log('App Launch - 用户已登录，立即加载权限');

				// 使用权限服务加载权限
				permissionService.loadUserPermissions(true)
					.then(res => {
						console.log('App Launch - 权限加载成功:', res);
					})
					.catch(err => {
						console.error('App Launch - 权限加载失败:', err);
					});
			}

			// 启动权限变更检查定时器
			this.startPermissionChangeCheck();
			// // #ifdef APP-PLUS

			// 	  plus.android.importClass("net.SocketExample");
			// 	  let socketExample = new net.SocketExample();
			// 	  socketExample.connect("192.168.0.1", 8080);

			//       // 签名证书检验
			//       var platform = uni.getSystemInfoSync().platform;
			//       var sign = plus.navigator.getSignature();
			// 	  console.log(platform, sign, uni.getSystemInfoSync);
			//       if('android'==platform){   //Android平台
			//         var sha1 = 'baad093a82829fb432a7b28cb4ccf0e9f37dae58';  //修改为自己应用签名证书SHA-1值，是全小写并且中间不包含“:”符号
			//         if(sha1!=sign){
			//           //证书不对时退出应用
			//           plus.runtime.quit();
			//         }
			//       }else{    //iOS平台
			//         var md5 = 'a2e629f0ea915b4ed11e296a059c9a12';   //修改为自己应用Apple Bunld ID(AppID)的md5值
			//         if(md5!=sign){
			//           //不进入应用或循环弹出提示框
			//           console.log('应用被破坏，无法正常运行！');
			//           uni.showModal({
			//             title:'错误',
			//             content: '应用被破坏，无法正常运行！',
			//           });
			//         }
			//       }
			// // #endif


		},
		onShow: function() {
			console.log('App Show');
			// 获取当前页面信息
			try {
				const pages = getCurrentPages();
				const currentPage = pages.length > 0 ? pages[pages.length - 1] : null;
				const pagePath = currentPage ? currentPage.route : '';

				// 登录页面不需要检查登录状态和权限
				if(pagePath && pagePath != "pages/login/login_pwd" && pagePath != "pages/login/login_sms") {
					// 检查登录状态
					this.$H.checkLoginAndJumpStart();
					this.$H.refrenceToken();

					// 获取token和用户信息
					const token = uni.getStorageSync('token');
					const userInfo = uni.getStorageSync('user');

					// 如果已登录，检查权限缓存是否存在
					if (token && userInfo) {
						const permissionsCache = permissionService.getPermissionsFromCache
							? permissionService.getPermissionsFromCache()
							: getPermissionsFromCache();

						// 简化版本：如果没有权限缓存，立即加载权限
						if (!permissionsCache) {
							console.log('App.vue - 检测到有用户登录但无权限缓存，立即加载权限');
							permissionService.loadUserPermissions(true)
								.then(res => {
									console.log('App.vue - 权限加载成功:', res);

									// 权限加载成功后检查当前页面权限
									if (currentPage && pagePath) {
										console.log('App.vue - 检查当前页面权限:', pagePath);
										permissionService.checkCurrentPagePermissionAfterLoad();
									}
								})
								.catch(err => {
									console.error('App.vue - 权限加载失败:', err);
								});
						} else {
							// 如果有权限缓存，检查权限变更
							this.checkPermissionChanges();

							// 检查当前页面权限
							if (currentPage && pagePath) {
								console.log('App.vue - 检查当前页面权限:', pagePath);
								permissionService.checkCurrentPagePermissionAfterLoad();
							}
						}
					}
				}
			} catch (e) {
				console.error('App.vue - onShow 执行出错:', e);
			}
		},
		onHide: function() {
			console.log('App Hide');
			this.$H.cleanTimer();
		},
		methods: {
			/**
			 * 启动权限变更检查定时器
			 * 每隔一定时间检查一次权限是否有变更
			 */
			startPermissionChangeCheck() {
				// 清除已有的定时器
				this.stopPermissionChangeCheck();

				// 使用权限服务启动权限变更检查定时器
				this.permissionCheckTimer = permissionService.startPermissionChangeCheck(10 * 60 * 1000); // 10分钟
			},

			/**
			 * 停止权限变更检查定时器
			 */
			stopPermissionChangeCheck() {
				if (this.permissionCheckTimer) {
					clearInterval(this.permissionCheckTimer);
					this.permissionCheckTimer = null;
				}
			},

			/**
			 * 检查权限是否有变更
			 * 如果有变更，则重新加载权限并通知用户
			 */
			/**
			 * 获取当前路由路径
			 *
			 * @returns {String|null} 当前路由路径，如果获取失败则返回null
			 */
			getCurrentRoute() {
				try {
					const pages = getCurrentPages();
					const currentPage = pages.length > 0 ? pages[pages.length - 1] : null;
					return currentPage ? currentPage.route : null;
				} catch (e) {
					console.error('获取当前路由出错:', e);
					return null;
				}
			},

			checkPermissionChanges() {
				// 使用权限服务检查权限变更并更新
				permissionService.checkPermissionChangesAndUpdate()
					.then(result => {
						// 如果权限有变更，刷新当前页面
						if (result && result.hasChanges) {
							// 刷新当前页面
							try {
								const pages = getCurrentPages();
								const currentPage = pages.length > 0 ? pages[pages.length - 1] : null;
								if (currentPage && currentPage.$vm) {
									// 如果当前页面有 onLoad 方法，重新调用
									if (typeof currentPage.$vm.onLoad === 'function') {
										currentPage.$vm.onLoad(currentPage.options || {});
									}

									// 如果当前页面有 onShow 方法，重新调用
									if (typeof currentPage.$vm.onShow === 'function') {
										currentPage.$vm.onShow();
									}
								}
							} catch (e) {
								console.error('刷新当前页面出错:', e);
								// 如果刷新页面失败，尝试重新加载页面
								const currentRoute = this.getCurrentRoute();
								if (currentRoute) {
									setTimeout(() => {
										uni.redirectTo({
											url: '/' + currentRoute
										});
									}, 100);
								}
							}
						}
					})
					.catch(err => {
						console.error('检查权限变更出错:', err);
					});
			}
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import './uni.scss';
	/* #ifndef APP-NVUE */
	@import '@/static/customicons.css';
	// 设置整个项目的背景色
	page {
		background-color: #f5f5f5;
	}

	/* #endif */
	.example-info {
		font-size: 14px;
		color: #333;
		padding: 10px;
	}

	.test {
		font-size: 14px;
		color: $uni-text-color;
		padding: 10px;
	}

	// :deep(.uni-page-head){
	// 	background: #2b2b2b !important;
	// 	color: #fff !important;
	// 	.uni-page-head-btn{
	// 		filter: invert(100%);
	// 	}
	// }

</style>
