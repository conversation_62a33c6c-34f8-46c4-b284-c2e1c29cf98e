import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import http from './common/axios.js'
import config from './common/config.js'
import cache from './common/cache.js'
import eventBus from './common/event-bus.js'
import { createSSRApp } from 'vue'
import CustomComponents from './components/index.js'
export function createApp() {
  const app = createSSRApp(App)
  app.config.globalProperties.$H = http
  app.config.globalProperties.$C = config
  app.config.globalProperties.$cache = cache
  app.config.globalProperties.$eventBus = eventBus
  app.use(CustomComponents)
  return {
    app
  }
}
// #endif