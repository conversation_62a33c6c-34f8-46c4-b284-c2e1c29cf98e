<template>
	<view class="content">
		<view style="height:60rpx;"></view>
		<view style="display: flex;justify-content: center; align-items: center; flex-direction: column;">
			<text class="u-demo-block__title" style="margin-top: 25rpx;font-size: 60rpx; color: #fff;">移动运维管理平台</text>
			<text class="u-demo-block__title"
				style="margin-top: 25rpx;font-size: 32rpx;color: #fff;">随时随地访问，高效便捷运维</text>
		</view>

		<image style="
		    width: 376px;
			height: 388rpx;
			margin: 0 auto;
			display: flex;
			margin-top: 100rpx;
		" src="/static/images_new/login/u2417.svg" mode="scaleToFill" />
		<!-- <view style="display: flex;justify-content: center; align-items: center; flex-direction: column;">
			<text class="u-demo-block__title" style="margin-top: 25rpx;font-size: 20pt;">账号密码登录</text>
			<text class="u-demo-block__title"
				style="margin-top: 25rpx;font-size: 10pt;color: #5A7096;">请使用已注册的账号密码</text>
		</view> -->
		<uni-forms ref="loginFormRef" :rules="customRules" :modelValue="loginForm" style="padding: 40px;">
			<uni-forms-item style="margin-bottom: 0;" name="account">
				<uni-easyinput class="uni-easyinput-input" prefix-icon="person" v-model="loginForm.account"
					placeholder="请输入用户帐号" />
			</uni-forms-item>
			<uni-forms-item name="checkPass">
				<uni-easyinput class="uni-easyinput-input" prefix-icon="locked" v-model="loginForm.checkPass"
					type="password" placeholder="请输入登录密码" />
			</uni-forms-item>
			<!-- <uni-forms-item v-if="isCode" name="verifyCode">
				<uni-easyinput prefix-icon="locked" v-model="loginForm.verifyCode" placeholder="请输入验证码">
					<template #right>
						<image :src="'data:image/png;base64,' + codeIco" class="image-code" @click="getCode"></image>
					</template>
</uni-easyinput>
</uni-forms-item> -->
			<!-- <uni-forms-item>
				<checkbox-group @change="setAgree">
					<checkbox :checked="isAgree" style="transform: scale(0.7);margin-left: 40px;" />
					<text class="tui-color-primary" @click="toUserAgreement">用户服务协议</text>
					<text class="tui-color-primary">、</text>
					<text class="tui-color-primary" @click="toPrivacyAgreement">隐私政策</text>
				</checkbox-group>
			</uni-forms-item> -->
			<uni-forms-item>
				<button type="primary" style="margin-top: 40px;" shape="circle" :disabled="disabledNew"
					:loading="logining" @click="verificationUser">
					登录
				</button>
			</uni-forms-item>
			<uni-forms-item>
				<view style="text-align: center;">
					<text style="margin-top: 20px;color: #fff; font-weight: 550;" @click="toSmsLogin">
						使用手机验证码登录
					</text>
				</view>
			</uni-forms-item>


		</uni-forms>

		<!-- <view class="tui-cell-text">
			<view class="tui-color-primary" hover-class="tui-opcity" :hover-stay-time="150" @tap="href(1)">忘记密码？</view>
			<view hover-class="tui-opcity" :hover-stay-time="150">
				没有账号？
				<text class="tui-color-primary" @tap="href(2)">注册</text>
			</view>
		</view> -->
		<!-- <view style="display: flex;justify-content: center;align-items: center;margin-top: 20px;">
			<u-divider style="width: 50%;" text="短信登录方式"></u-divider>
		</view>
		<view class="tui-auth-login" style="display: flex;justify-content: center;align-items: center;">
			<view class="tui-icon-platform" hover-class="tui-opcity" :hover-stay-time="150" @tap="toSmsLogin">
				<image src="/static/images/sms.png" class="tui-login-logo"></image>
			</view>
		</view> -->
	</view>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watchEffect, getCurrentInstance } from 'vue' // 1. 添加getCurrentInstance导入
import md5 from 'js-md5'
import { sm3 } from 'sm-crypto'
import $C from '@/common/config.js'
import securityStorage from '@/common/securityStorage'
import tuiIcon from "@/components/tui/tui-icon"
import tuiBottomPopup from "@/components/tui/tui-bottom-popup"
import permissionService from '@/common/permission-service.js'
import globalState from '@/common/global-state.js'

// 响应式数据迁移
const loginForm = reactive({
	account: "",
	checkPass: "",
	verifyCode: "",
	rnd: "",
	isRemember: false,
})
const isAgree = ref(true)
const isCode = ref(false)
const codeIco = ref("")
const logining = ref(false)

// 计算属性改造
const disabledNew = computed(() => {
	// 简化逻辑直接返回条件判断，确保响应式依赖被正确追踪
	return !(loginForm.account && loginForm.checkPass)
})

// 生命周期管理
onMounted(() => {
	// 原mounted逻辑保留
})

// 处理onReady逻辑
onMounted(() => {
	getCode()
})

// 监听loginForm变化
watchEffect(() => {
	// 原watch loginForm逻辑保留
})

// 方法迁移方案
const toUserAgreement = () => {
	uni.navigateTo({ url: "./agreement" })
}
const toPrivacyAgreement = () => {
	uni.navigateTo({ url: "./agreement_privacy" })
}
const setAgree = (e) => {
	isAgree.value = !isAgree.value
	// 组件通信改造：使用defineExpose暴露
	defineExpose({ isAgree })
}
const clearInput = (type) => {
	if (type === 1) loginForm.account = ''
	else loginForm.checkPass = ''
}

// 获取验证码
const getCode = () => {
	// const that = getCurrentInstance()?.proxy
	// that.$H.get($C.portalContextPath + '/login/verifycode', { id: 5 }).then(res => {
	// 	if (res.status === "0") {
	// 		isCode.value = res.data.requireVerifyCode !== 'false'
	// 		if (isCode.value) {
	// 			codeIco.value = res.data.image
	// 			loginForm.rnd = res.data.rnd
	// 		}
	// 	}
	// })
}

// 登录验证核心逻辑
const verificationUser = () => {
	if (loginForm.account.length < 2) {
		showMessage("帐号长度不少于2")
		return
	}
	if (loginForm.checkPass.length < 4) {
		showMessage("口令长度不少于4")
		return
	}
	logining.value = true
	const pwd = sm3(loginForm.checkPass)
	const boundary = '----WebKitFormBoundary' + Math.random().toString(16).substr(2)
	let data = `--${boundary}\r\n`
	data += 'Content-Disposition: form-data; name="u"\r\n\r\n'
	data += loginForm.account + '\r\n'
	data += `--${boundary}\r\n`
	data += 'Content-Disposition: form-data; name="p"\r\n\r\n'
	data += pwd + '\r\n'
	data += `--${boundary}--`
	let url = ''
	// #ifndef APP-PLUS
	url = '/new'
	// #endif
	console.log('手机-iphone', $C.getBaseUrl() + url + '/login')
	let tmpurl = '/login'
	// #ifndef H5
	tmpurl = 'login'
	// #endif
	uni.request({
		url: $C.getBaseUrl() + url + tmpurl,
		method: "POST",
		sslVerify: false,
		data: data,
		dataType: 'json', //设置json返回值就会json.parse
		header: {
			"Content-Type": `multipart/form-data; boundary=${boundary}`
		},
		success(res) {
			console.log(res)
			let isLoginSuccess = false;
			if (res.data.status == "0") {
				let token = res.data.data

				if (!token || !token.startsWith("Bearer ")) {
					console.log("登陆异常,无效的token")
					uni.showToast({
						title: "登陆异常,无效token",
						icon: "none"
					})
					return
				}
				try {
					let msg = res.data.msg
					let isObject = msg && typeof JSON.parse(msg) == 'object'
					if (!isObject) {
						uni.showToast({
							title: "登陆异常",
							icon: "none"
						})
						return
					}
				} catch (e) {
					console.log(e)
					uni.showToast({
						title: "登陆异常",
						icon: "none"
					})
					return
				}
				console.log("登陆成功", res.data)

				uni.setStorageSync('token', token)
				// self.getUserInfo(JSON.parse(res.data.msg).userId);
				securityStorage.setStorageSync('user', res.data.msg)
				const instance = getCurrentInstance(); // 2. 获取当前实例
				instance?.proxy?.$H.refrenceToken()//开始调用定时刷新token // 3. 替换self.$H为instance?.proxy?.$H
				logining.value = false // 4. 替换self.logining为响应式变量
				uni.setStorageSync('user', res.data.msg)

				// 登录成功后立即加载权限 - 简化版本
				console.log('登录成功，强制更新权限');

				// 清除所有权限缓存
				permissionService.clearPermissionsAfterLogout();

				// 强制加载最新权限
				permissionService.loadUserPermissions(true)
					.then(() => {
						console.log('登录后权限强制更新成功');

						// 显示登录成功提示
						uni.showToast({
							title: "登录成功",
							icon: "none"
						});

						logining.value = false;
						isLoginSuccess = true;

						// 设置全局状态，标记需要刷新页面
						try {
							const userData = JSON.parse(res.data.msg);
							console.log('设置全局状态，标记需要刷新页面');
							globalState.setLoginSuccess(userData.userId);
						} catch (e) {
							console.error('解析用户数据失败:', e);
						}

						// 跳转到首页
						uni.switchTab({
							url: '/pages/index'
						});
					})
					.catch(err => {
						console.error('登录后权限更新失败:', err);

						// 即使权限加载失败，也允许用户登录
						uni.showToast({
							title: "登录成功",
							icon: "none"
						});

						logining.value = false;
						isLoginSuccess = true;

						// 设置全局状态，标记需要刷新页面
						try {
							const userData = JSON.parse(res.data.msg);
							console.log('设置全局状态，标记需要刷新页面');
							globalState.setLoginSuccess(userData.userId);
						} catch (e) {
							console.error('解析用户数据失败:', e);
						}

						// 跳转到首页
						uni.switchTab({
							url: '/pages/index'
						});
					});


			} else if (res.data.status == '3') {
				// console.log("登陆异常", res);
				uni.showToast({
					title: res.data.msg,
					icon: "none",
					duration: 4000
				});
				// self.getCode();
			} else {
				console.log("登陆异常", res);
				uni.showToast({
					title: res.data.msg,
					icon: "none"
				})
			}
			logining.value = false;
		},

		fail: err => {
			uni.showModal({
				content: err.errMsg ? err.errMsg : "发生异常，请联络管理员"
			})
			logining.value = false; // 5. 替换self.logining为响应式变量
		},
	})
}

const showMessage = (checkRes) => {
	uni.showToast({
		title: checkRes,
		icon: "none"
	});
}

const checkAndCleanData = () => {
	let token = uni.getStorageSync('token');
	if (token) {
		// 清除权限缓存
		permissionService.clearPermissionsAfterLogout();

		// 清除其他缓存
		uni.removeStorageSync('token');
		securityStorage.removeStorageSync('user');
		uni.removeStorageSync('config');

		console.log('登录前清除所有缓存，确保使用新用户的权限');
	}
}

const toSmsLogin = () => {
	uni.navigateTo({
		url: "./login_sms"
	})
}

const href = (page) => {
	let url = "";
	switch (page) {
		case 1:
			url = "./verify_mobile"
			break;
		case 2:
			url = "./register"
			break;
		case 3:
			url = "./login_sms"
			break;
		default:
			break;
	}
	uni.navigateTo({
		url: url
	})
}

</script>

<style lang="scss" scoped>
@import '@/uni.scss';

.content {
	position: relative;
	background: url('@/static/images_new/login/u2414.svg') no-repeat fixed;
	background-size: cover;
}

.tui-icon-close {
	margin: 0 auto
}

.bg-img {
	background: url('@/static/bg.png') no-repeat bottom left fixed;
	position: fixed;
	background-size: contain;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	z-index: -1;
}

.tui-login-way {
	width: 100%;
	font-size: 26rpx;
	color: $uni-text-color;
	display: flex;
	justify-content: center;
	position: fixed;
	left: 0;
	bottom: 180rpx;

	view {
		padding: 12rpx 10rpx;
	}
}

.tui-auth-login {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	padding-bottom: 80rpx;
	padding-top: 0rpx;

	.tui-icon-platform {
		width: 90rpx;
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		margin-left: 0rpx;

		&::after {
			content: '';
			position: absolute;
			width: 200%;
			height: 200%;
			transform-origin: 0 0;
			transform: scale(0.5, 0.5) translateZ(0);
			box-sizing: border-box;
			left: 0;
			top: 0;
			border-radius: 180rpx;
			border: 1rpx solid $uni-text-color-placeholder;
		}
	}

	.tui-login-logo {
		width: 60rpx;
		height: 60rpx;
	}
}

.tui-btn-send {
	width: 162rpx;
	height: 50rpx;
	text-align: center;
	padding-top: 10rpx;
	flex-shrink: 0;
	font-size: $uni-font-size-base;
	color: #ffffff;
	background: #95afc0; //
}

.tui-gray {
	background: #c09ee0;
	padding-top: 20rpx;
}

.image-code {
	width: 200rpx;
	height: 28px;
	margin-left: -200rpx;
}

:deep(.uni-easyinput__content) {
	border-radius: 0 !important;
}

::v-deep .uni-easyinput-input {
	uni-view {
		border-radius: 0;
		height: 90rpx;
	}
}
</style>
