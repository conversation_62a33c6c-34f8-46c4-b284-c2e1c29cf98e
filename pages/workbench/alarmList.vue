<template>
	<view class="u-page">
		<view style="width: 100%;">
			<u-row>
				<u-col span="9" offset="0">
					<u-search style="margin: 20px;width: 100%;" :show-action="false" @change="search">
					</u-search>
				</u-col>
			</u-row>
			<u-list-item>
				<u-cell>
					<text slot="icon" @click="choose('全部')" style="font-size: 12px;">全部 {{ severity.activeCount }}</text>
					<u-text slot="icon" prefixIcon="/static/asset/u314.png" iconStyle="font-size: 5px"
						style="padding-left: 18px;"></u-text>
					<text slot="icon" @click="choose('严重')" style="margin-left: 5px;font-size: 12px;">
						严重 {{ severity.seriousCount }}
					</text>
					<u-text slot="icon" prefixIcon="/static/asset/u315.png" iconStyle="font-size: 5px"
						style="padding-left: 18px;"></u-text>
					<text slot="icon" @click="choose('重要')" style="margin-left: 5px;font-size: 12px;">
						重要 {{ severity.importantCount }}
					</text>
					<u-text slot="icon" prefixIcon="/static/asset/u316.png" iconStyle="font-size: 5px"
						style="padding-left: 18px;"></u-text>
					<text slot="icon" @click="choose('一般')" style="margin-left: 5px;font-size: 12px;">
						一般 {{ severity.slightCount }}
					</text>
					<u-text slot="icon" prefixIcon="/static/asset/u317.png" iconStyle="font-size: 5px"
						style="padding-left: 18px;"></u-text>
					<text slot="icon" @click="choose('提示')" style="margin-left: 5px;font-size: 12px;">
						提示 {{ severity.reminderCount }}
					</text>
					<!-- <u-text slot="icon" prefixIcon="arrow-up" style="padding-left: 27px;" v-show="!showMoreRecord" @click="showMoreRecords()">
					</u-text>
					<u-text slot="icon" prefixIcon="arrow-down" style="padding-left: 27px;" v-show="showMoreRecord" @click="showMoreRecords()">
					</u-text> -->
				</u-cell>
			</u-list-item>
			<u-list @scrolltolower="scrolltolower">
				<u-list-item v-for="(item, index) in dataMap" :key="index">
					<u-cell @click="openDetail(item)" isLink>
						<text slot="icon" v-if="item.reSeverity == '严重'" class="u-demo-block__title"
							style="color: red;">{{ item.reSeverity }}</text>
						<text slot="icon" v-else-if="item.reSeverity == '重要'" class="u-demo-block__title"
							style="color:#ff8f17">{{ item.reSeverity }}</text>
						<text slot="icon" v-else-if="item.reSeverity == '一般'" class="u-demo-block__title"
							style="color:#00e320">{{ item.reSeverity }}</text>
						<text slot="icon" v-else class="u-demo-block__title"
							style="color:#00bd81">{{ item.reSeverity }}</text>
						<text slot="icon" class="u-demo-block__title"
							style="margin-left: 15px;color: dimgrey;">{{ item.title}}</text>
					</u-cell>
					<u-cell @click="openPopup(item.description)" v-show="showMoreRecord">
						<text slot="icon" class="u-demo-block__title" style="margin-left: 15px;">
							<view style="overflow-x: hidden;text-overflow: ellipsis;white-space: nowrap;width: 80%;">告警正文：{{item.description}}</view>
						</text>
					</u-cell>
					<u-cell v-show="showMoreRecord">
						<text slot="icon" class="u-demo-block__title" style="margin-left: 15px;">发生时间：{{ item.firstOccurrence }}</text>
					</u-cell>
					<u-cell v-show="showMoreRecord">
						<text slot="icon" class="u-demo-block__title"
							style="margin-left: 15px;">资产IP：{{ item.devIp}}</text>
					</u-cell>
				</u-list-item>
			</u-list>
			<u-popup :safeAreaInsetBottom="true" :safeAreaInsetTop="true" mode="center" :show="show"
				:round="popupData.round" :overlay="popupData.overlay" :borderRadius="popupData.borderRadius"
				:closeable="popupData.closeable" :closeOnClickOverlay="popupData.closeOnClickOverlay" @close="show = false">
				<view class="u-popup-slot" :style="{
					width: '85%',
					height: '80%',
					overflow: auto,
					marginTop: '20px',marginLeft: '30px'
				}">
					<view>
						<text class="u-demo-block__title">告警正文</text>
						<u-line customStyle="margin:10px 0px 10px -15px"></u-line>
					</view>
					<scroll-view scroll-y="true" style="height: 500rpx;">
						 <text class="u-demo-block__title">{{alertMessage}}</text>
					</scroll-view>
				</view>
			</u-popup>
			<u-popup :safeAreaInsetBottom="true" :safeAreaInsetTop="true" :mode="popupData.mode" :show="detailShow" height="2500"
				:round="popupData.round" :overlay="popupData.overlay" :borderRadius="popupData.borderRadius"
				:closeable="popupData.closeable" :closeOnClickOverlay="popupData.closeOnClickOverlay" @close="detailShow = false">
				<view class="u-popup-slot" :style="{
					width: '100vw',
					height: '100vh',
					overflow: auto,
					marginTop: '20px',marginLeft: '30px'
				}">
					<view>
						<view style="display: flex;justify-content: center;">
							<text class="u-demo-block__title" style="font-size: 20px;margin-bottom: 20px;">告警详情</text>
						</view>
					</view>
					<uni-list>
						<uni-list-item class="item" title="告警标题" :rightText="detail.title">
						</uni-list-item>
						<uni-list-item class="item" title="告警等级" :rightText="detail.reSeverity">
							<!-- <view v-slot="rightText">
								<text slot="icon" v-if="detail.reSeverity == '严重'" class="u-demo-block__title"
									style="color: red;">{{ detail.reSeverity }}</text>
								<text slot="icon" v-else-if="detail.reSeverity == '重要'" class="u-demo-block__title"
									style="color:#ff8f17">{{ detail.reSeverity }}</text>
								<text slot="icon" v-else class="u-demo-block__title"
									style="color:#00e320">{{ detail.reSeverity }}</text>
							</view> -->
						</uni-list-item>
						<uni-list-item class="item" title="告警唯一ID" :rightText="detail.alertId">
						</uni-list-item>
						<uni-list-item class="item" title="持续时长" :rightText="detail.duration">
						</uni-list-item>
						<uni-list-item class="item" title="告警重复次数" :rightText="detail.tally">
						</uni-list-item>
						<!-- <uni-list-item class="item" title="告警备注" :rightText="detail.description">
						</uni-list-item> -->
						<uni-list-item class="item" title="告警首次发现时间" :rightText="detail.firstOccurrence">
						</uni-list-item>
						<uni-list-item class="item" title="告警最新发现时间" :rightText="detail.lastOccurrence">
						</uni-list-item>
						<uni-list-item class="item" title="资产IP" :rightText="detail.devIp">
						</uni-list-item>
						<!-- <uni-list-item class="item" title="定位对象" rightText="">
						</uni-list-item>
						<uni-list-item class="item" title="工单号" rightText="">
						</uni-list-item>
						<uni-list-item class="item" title="设备名" rightText="">
						</uni-list-item>
						<uni-list-item class="item" title="资产责任人" rightText="">
						</uni-list-item> -->
					</uni-list>
					
					<text style="font-size: 12px;margin-left: 15px;">告警正文</text>
					<view style="display: flex;justify-content: center;width: 90%;">
						<!-- <u--textarea disabled customStyle="width: 80%;" v-model="detail.description"></u--textarea> -->
						<scroll-view scroll-y="true" style="margin-left: 40px;height: 100%;margin-top: 15px;">
							 <view class="alarm-detail">{{detail.description}}</view>
						</scroll-view>
					</view>
					<u-line customStyle="margin-top: 15px"></u-line>
				</view>
			</u-popup>
			<u-overlay :show="showSlot" @click="showSlot = !showSlot">
				<view class="overlay-wrap">
					<view class="overlay-wrap__box"></view>
				</view>
			</u-overlay>
		</view>
	</view>
</template>

<script>
  import securityStorage from '@/common/securityStorage'
	export default {
		data() {
			return {
				user: [],
				indexList: [],
				dataMap: [],
				origDataMap: [], //原始集合
				alarmInfo: null,
				selectIds: "",
				data: [],
				total: 0,
				pageNum: 1,
				pageSize: 40,
				tableLoading: true,
				currentSeverity: "all",
				currentTab: "all",
				lastIndex: 0,
				loadTable: false,
				activeCount: 0,
				confirmCount: 0,
				allCount: 0,
				levelMap: null,
				cancelCount: 0,
				severity: {
					allCount: 0,
					seriousCount: 0,
					importantCount: 0,
					slightCount: 0,
					reminderCount: 0,
					unknownCount: 0,
				},
				allCondition: {
					titleInput: "",
					title: "",
				},
				activeCondition: {
					titleInput: "",
					title: "",
				},
				confirmCondition: {
					titleInput: "",
					title: "",
				},
				cancelCondition: {
					titleInput: "",
					title: "",
				},
				alertMessage: "",
				show: false,
				detailShow: false,
				detail: [],
				popupData: {
					overlay: true,
					mode: 'right',
					borderRadius: '',
					round: 10,
					closeable: true,
					closeOnClickOverlay: true
				},
				showMoreRecord: true,
				showSlot: false,
			}
		},
		onLoad() {
			this.$H.checkLoginAndJumpStart();//检查登录状态
			this.user = securityStorage.getStorageSync('user');// uni.getStorageSync('user');
			this.loadmore()
		},
		methods: {
			openDetail(item) {
				this.detailShow = !this.detailShow;
				this.getAlarmDetail(item);
			},
			// 获取告警详情
			getAlarmDetail(item) {
				uni.showLoading({
					title: '详情加载中'
				})
				setTimeout(() => {
					uni.hideLoading();//加载超时设置
				}, 3000)
				this.detail = [];
				this.$H.get('/alarm-query-server/alarm/getByAlertId/' , {
					alertId: item.alertId,
					code: "alarmDetailsactive",
					user: this.user.用户账号,
				}).then(res => {
					if (res.status == "0") {
						console.log("getAlarmDetail", res.data);
						this.detail = res.data;
						uni.hideLoading();
					}
				})
			},
			openPopup(message) {
				return;//真机打开慢，暂不启用
				this.show = !this.show
				this.alertMessage = message;
			},
			search(value) {
				this.dataMap = this.origDataMap.filter(item => item.title.includes(value));
			},
			choose(value) {
				if ("全部" === value) {
					this.dataMap = this.origDataMap;
				} else {
					this.dataMap = this.origDataMap.filter(item => item.reSeverity == value);
				}
			},
			showMoreRecords() {
				this.showMoreRecord = !this.showMoreRecord;
			},
			scrolltolower() {
				// this.loadmore();//此处不刷新
			},
			loadmore() {
				this.queryAlarm();
			},
			queryAlarm() {
				let current = this;
				var $chars = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678";
				var maxPos = $chars.length;
				var pwd = "";
				for (let i = 0; i < 10; i++) {
					pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
				}
				let param = {
					"user": this.user.用户账号,
					"code": "realTimeAlarmactive",
					"ruleId": "",
					"ruleName": "",
					"severity": "all",
					"page": 1,
					"browserId": "browser:805585",
					"status": "active",
					"maxsize": 9999,
					"size": 9999,
					"overtime": "",
					"descType": "",
					"orderBy": "",
					"allParam": {
						"titleInput": "",
						"title": "",
						"overtime": ""
					},
					"activeParam": {
						"titleInput": "",
						"title": "",
						"overtime": "",
						"ciTypeId": "",
						"ticketFlag": null,
						"ticketId": null,
						"ticketStatus": null,
						"ticketSendTime": null,
						"location": null,
						"ciVendor": null,
						"tally": null,
						"duration": null,
						"status": null
					},
					"confirmParam": {
						"titleInput": "",
						"title": "",
						"overtime": ""
					},
					"cancelParam": {
						"titleInput": "",
						"title": "",
						"overtime": ""
					},
					"params": {
						"reSeverity": ""
					},
					"from": 20600,
					"resource_manage_tenant": "",
					"platformid": ""
				};
				uni.showLoading({
					title: '加载中'
				})
				current.$H.post('alarm-query-server/alarm/findTimeAm', param).then(res => {
					if (res.status == "0") {
						this.dataMap = res.data.dataMap.data;
						this.origDataMap = res.data.dataMap.data;
						this.severity.activeCount = res.data.activeCount;
						this.severity.importantCount = res.data.importantCount;
						this.severity.reminderCount = res.data.reminderCount;
						this.severity.seriousCount = res.data.seriousCount;
						this.severity.slightCount = res.data.slightCount;
						this.severity.unknownCount = res.data.unknownCount;
						this.choose("严重");
						if(this.dataMap.length == 0){
							this.choose("全部");
						}
					}
					console.log("###", res);
				});
				setTimeout(function() {
					uni.hideLoading()
				}, 2000)
			},
		},
	}
</script>

<style lang="scss">
	.u-page {
		padding: 0;
	}
	.overlay-wrap {
		@include flex;
		justify-content: center;
		align-items: center;
		flex: 1;
	
		&__box {
			width: 200rpx;
			height: 200rpx;
			background-color: #70e1f5;
		}
	}
	
	.alarm-detail {
		font-size: 12px;
		color: darkgrey;
	}
</style>
