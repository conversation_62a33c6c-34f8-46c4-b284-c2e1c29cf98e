<template>
    <view class="container">
        <view>
            <view style="padding:16rpx">资源数量统计</view>
            <uni-row>
                <uni-col :span="8"
                    style="font-size: 30rpx;display: flex;align-items: center;color: #666;justify-content: space-around;">
                    <image style="height: 66rpx;width: 66rpx;" src="/static/images_new/server.png" mode="scaleToFill" />
                    服务器 <span style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">113</span>
                </uni-col>
                <uni-col :span="8"
                    style="font-size: 30rpx;display: flex;align-items: center;color: #666;justify-content: space-around;">
                    <image style="height: 66rpx;width: 66rpx;" src="/static/images_new/server.png" mode="scaleToFill" />
                    中间件 <span style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">13</span>
                </uni-col>
                <uni-col :span="8"
                    style="font-size: 30rpx;display: flex;align-items: center;color: #666;justify-content: space-around;">
                    <image style="height: 66rpx;width: 66rpx;" src="/static/images_new/clound.png" mode="scaleToFill" />
                    服务器 <span style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">113</span>
                </uni-col>
                <uni-col :span="8"
                    style="font-size: 30rpx;display: flex;align-items: center;color: #666;padding-top: 16rpx;justify-content: space-around;">
                    <image style="height: 66rpx;width: 66rpx;" src="/static/images_new/clound.png" mode="scaleToFill" />
                    虚拟机 <span style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">114</span>
                </uni-col>
                <uni-col :span="8"
                    style="font-size: 30rpx;display: flex;align-items: center;color: #666;padding-top: 16rpx;justify-content: space-around;">
                    <image style="height: 66rpx;width: 66rpx;" src="/static/images_new/clound.png" mode="scaleToFill" />
                    云平台 <span style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">4</span>
                </uni-col>
                <uni-col :span="8"
                    style="font-size: 30rpx;display: flex;align-items: center;color: #666;padding-top: 16rpx;justify-content: space-around;">
                    <image style="height: 66rpx;width: 66rpx;" src="/static/images_new/clound.png" mode="scaleToFill" />
                    应用服务 <span style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">14</span>
                </uni-col>
            </uni-row>
        </view>

        <view style="margin-top: 26rpx;">
            <span>资源负载态势</span>
            <view style="display: flex;flex-wrap: wrap;height: 400rpx;">
                <view style="width: 216rpx;height: 166rpx;display: flex;align-items: center;">
                    <img src="/static/images_new/cpu.png" alt="" style="width: 100rpx;height: 100rpx;">
                    <view style="font-size: 26rpx;">
                        <view>73622</view>
                        <view style="color: #aaa;">cpu数量</view>
                    </view>
                </view>
                <view style="width: 216rpx;height: 166rpx;display: flex;align-items: center;">
                    <img src="/static/images_new/memory.png" alt="" style="width: 100rpx;height: 100rpx;">
                    <view style="font-size: 26rpx;">
                        <view>166 GB</view>
                        <view style="color: #aaa;">内存容量</view>
                    </view>
                </view>
                <view style="width: 216rpx;height: 166rpx;display: flex;align-items: center;">
                    <img src="/static/images_new/memory.png" alt="" style="width: 100rpx;height: 100rpx;">
                    <view style="font-size: 26rpx;">
                        <view>666 GB</view>
                        <view style="color: #aaa;">磁盘容量</view>
                    </view>
                </view>
                <view
                    style="width: 216rpx;height: 166rpx; display: flex;flex-direction: column;flex-shrink: 0;margin-top: -40rpx;">
                    <view style="height: 166rpx; flex-shrink: 0;flex-grow: 1;">
                        <l-echart style="flex-shrink: 0;" ref="chartRef01"></l-echart>
                    </view>
                    <view style="text-align: center; font-size: 23rpx;">cpu利用率</view>
                </view>
                <view style="width: 216rpx;height: 166rpx;margin-top: -40rpx;">
                    <view style="height: 166rpx;">
                        <l-echart ref="chartRef02"></l-echart>
                    </view>
                    <view style="text-align: center; font-size: 23rpx;">内存利用率</view>
                </view>
                <view style="width: 216rpx;height: 166rpx;margin-top: -40rpx;">
                    <view style="height: 166rpx;">
                        <l-echart ref="chartRef03"></l-echart>
                    </view>
                    <view style="text-align: center; font-size: 23rpx;">磁盘利用率</view>
                </view>
            </view>

            <view
                style="display: grid;grid-template-columns: repeat(3, 1fr);text-align: center;margin-top: 16rpx;cursor: pointer;">
                <view style="border: 2rpx solid #ddd;">日</view>
                <view style="border: 2rpx solid #ddd;">周</view>
                <view style="border: 2rpx solid #ddd;">月</view>
            </view>

            <view style="margin-top: 16rpx;">
                <view>
                    <view style="height: 650rpx">
                        <view>CPU利用率趋势图</view>
                        <l-echart ref="chartLine01"></l-echart>
                    </view>
                    <view style="height: 650rpx">
                        <view>内存利用率趋势图</view>
                        <l-echart ref="chartLine02"></l-echart>
                    </view>
                    <view style="height: 650rpx">
                        <view>磁盘利用率趋势图</view>
                        <l-echart ref="chartLine03"></l-echart>
                    </view>

                </view>
            </view>
        </view>
    </view>
</template>

<script>
import * as echarts from 'echarts'
export default {
    components: {
    },
    data() {
        return {
            option: {
                series: [{
                    type: 'gauge',
                    startAngle: 90,
                    endAngle: -270,
                    pointer: {
                        show: false
                    },
                    grid: {
                        left: 20,
                        right: 20,
                        bottom: 15,
                        top: 10,
                        containLabel: true
                    },
                    progress: {
                        show: true,
                        overlap: false,
                        roundCap: true,
                        clip: false,
                        itemStyle: {
                            // borderWidth: 1,
                            // borderColor: '#464646'
                        }
                    },
                    axisLine: {

                        lineStyle: {
                            width: 6
                        }
                    },
                    splitLine: {
                        show: false,
                        distance: 0,
                        length: 10
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        show: false,
                        distance: 50
                    },
                    data: [
                        {
                            value: 20,
                            detail: {
                                offsetCenter: ['0%', '0%']
                            }
                        }
                    ],
                    detail: {
                        // width: 50,
                        // height: 14,
                        fontSize: 13,
                        //color: 'auto',
                        // borderColor: 'auto',
                        // borderRadius: 20,
                        // borderWidth: 1,
                        formatter: '{value}%'
                    }
                }]
            },
            optionBrokenLine: {
                xAxis: {
                    type: 'category',
                    data: ['03-11', '03-13', '03-16', '03-19', '03-20', '03-22', '03-26']
                },
                yAxis: {
                    type: 'value'
                },
                series: [{
                    data: [150, 250, 190, 266, 333, 290, 366],
                    type: 'line',
                    smooth: true,
                    areaStyle: {}

                }]
            },
        }
    },

    // 页面加载
    onLoad(page) {
    },
    async mounted() {
        // setTimeout(() => {
        this.init();
        // }, 1000)
    },
    methods: {
        async init() {
            // chart 图表实例不能存在data里
            const chart01 = await this.$refs.chartRef01.init(echarts);
            chart01.setOption(this.option)
            const chart02 = await this.$refs.chartRef02.init(echarts);
            chart02.setOption(this.option)
            const chart03 = await this.$refs.chartRef03.init(echarts);
            chart03.setOption(this.option)
            this.$refs.chartLine01.init(echarts, chart => {
                chart.setOption(this.optionBrokenLine);
            });
            this.$refs.chartLine02.init(echarts, chart => {
                chart.setOption(this.optionBrokenLine);
            });
            this.$refs.chartLine03.init(echarts, chart => {
                chart.setOption(this.optionBrokenLine);
            });
        },
    },
    watch: {
    }
}
</script>

<style lang="scss" scoped>
.container {
    background-color: #fff;
    padding: 26rpx;
    height: 200vh;
    padding-bottom: 66rpx;

}

.app_server {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16rpx;

    view {
        height: 100rpx;
        border: 2rpx dashed #17a9ff;
        border-radius: 26rpx;
        line-height: 100rpx;
        text-align: center;
        color: #17a9ff;
        box-shadow: 3rpx 3rpx 6rpx;
    }
}

.basice_server {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;

    view {
        height: 166rpx;
        border: 2rpx dashed #17a9ff;
        border-radius: 26rpx;
        line-height: 166rpx;
        text-align: center;
        color: #17a9ff;
        box-shadow: 3rpx 3rpx 6rpx;
    }
}
</style>