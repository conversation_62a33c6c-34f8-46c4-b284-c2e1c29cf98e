<template>
    <view class="query-asset" style="background-color: #fff;">
        <scroll-view ref="tabScroll" :show-scrollbar="false" scroll-x class="tab-scroll" scroll-with-animation
            :scroll-left="scrollLeft">
            <view class="tab-bar">
                <!-- :class="{ 'active': activeIndex == index }" -->
                <view :ref="`tabItem${index}`" v-for="(item, index) in tabItems" :key="index" class="tab-item"
                    @click="switchTab(index)">
                    {{ item }}
                </view>
                <!-- 底部滑动条 -->
                <view ref="tabLine" class="tab-line" :style="lineStyle"></view>
            </view>
        </scroll-view>
        <swiper :current="activeIndex" @change="onSwiperChange" :duration="300">
            <swiper-item v-for="(item, index) in tabItems" :key="index">
                <view class="content">
                    <scroll-view class="list-content" scroll-y style="overflow: auto;" @scrolltolower="scrollBottom()">
                        <view v-show="item == '运行态势统计'" style="background-color: #fff;padding: 26rpx;height: 133vh;">
                            <view
                                style="background-color: #fff;display: grid;grid-template-columns: repeat(3, 1fr);text-align: center;margin-top: 16rpx;cursor: pointer;">
                                <view style="border: 2rpx solid #ddd;">日</view>
                                <view style="border: 2rpx solid #ddd;">周</view>
                                <view style="border: 2rpx solid #ddd;">月</view>
                            </view>
                            <view style="margin-top: 16rpx;">
                                <view style="padding: 26rpx 0;">告警数据统计</view>
                                <view style="display: flex;justify-content: space-around;">
                                    <image src="/static/images_new/alarm.png" mode="scaleToFill"
                                        style="width: 100rpx;height: 100rpx;border-radius: 16rpx;" />
                                    <view>
                                        <view style="font-size: 30rpx;color: #666;">总告警数</view>
                                        <view style="font-size: 43rpx;font-weight: 500;">336</view>
                                    </view>
                                    <view>
                                        <view style="font-size: 30rpx;color: #666;">紧急告警数</view>
                                        <view style="color: #f40;font-size: 43rpx;font-weight: 500;">336</view>
                                    </view>
                                    <view>
                                        <view style="font-size: 30rpx;color: #666;">重要告警数</view>
                                        <view style="color: orange;font-size: 43rpx;font-weight: 500;">336</view>
                                    </view>
                                </view>
                                <view style="display: flex;justify-content: space-around;margin-top: 26rpx;">
                                    <view
                                        style=" width: 166rpx;height: 100rpx;flex-grow: 0;flex-wrap: wrap;white-space: wrap;font-size: 30rpx;color: #666;margin-right: 16rpx;">
                                        故障平均时长(分钟)
                                    </view>
                                    <view style="line-height: 100rpx;flex-grow: 1;">
                                        <view style="font-size: 43rpx;font-weight: 500;">5</view>
                                    </view>
                                    <view style="line-height: 100rpx;flex-grow: 1;">
                                        <view style="color: #f40;font-size: 43rpx;font-weight: 500;">6</view>
                                    </view>
                                    <view style="line-height: 100rpx;flex-grow: 1;">
                                        <view style="color: orange;font-size: 43rpx;font-weight: 500;">6</view>
                                    </view>
                                </view>
                            </view>
                            <view style="margin-top: 66rpx;">
                                <view style="padding: 26rpx 0;">告警类别TOP5</view>
                                <view style="margin-top: -70rpx;">
                                    <view style="height: 566rpx">
                                        <l-echart ref="chartRef02"></l-echart>
                                    </view>
                                </view>
                            </view>

                            <view style="margin-top: -49rpx;">
                                <view style="padding: 26rpx 0;">
                                    告警综合关联统计
                                </view>
                                <view class="correlation_statistics" style="display: grid;font-size: 26rpx;color: #fff;font-weight: 500;
                                    grid-template-columns: repeat(2, 1fr);
                                    gap: 26rpx;
                                    ">
                                    <view class="item" style="background-color: #f40;">
                                        <view>认证场景</view>
                                        <view>25</view>
                                    </view>
                                    <view class="item" style="background-color: orange;">
                                        <view>通讯场景</view>
                                        <view>25</view>
                                    </view>
                                    <view class="item" style="background-color: #60b0f9;">
                                        <view>视频能力会议场景</view>
                                        <view>25</view>
                                    </view>
                                    <view class="item" style="background-color: #01c101;">
                                        <view>视频能力监控场景</view>
                                        <view>25</view>
                                    </view>
                                </view>

                                <view style="margin-top: 36rpx;">
                                    <uni-row>
                                        <uni-col :span="8"
                                            style="font-size: 30rpx;display: flex;align-items: center;color: #666;justify-content: space-around;">
                                            <image style="height: 66rpx;width: 66rpx;"
                                                src="/static/images_new/server.png" mode="scaleToFill" />
                                            服务器 <span
                                                style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">113</span>
                                        </uni-col>
                                        <uni-col :span="8"
                                            style="font-size: 30rpx;display: flex;align-items: center;color: #666;justify-content: space-around;">
                                            <image style="height: 66rpx;width: 66rpx;"
                                                src="/static/images_new/server.png" mode="scaleToFill" />
                                            中间件 <span
                                                style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">13</span>
                                        </uni-col>
                                        <uni-col :span="8"
                                            style="font-size: 30rpx;display: flex;align-items: center;color: #666;justify-content: space-around;">
                                            <image style="height: 66rpx;width: 66rpx;"
                                                src="/static/images_new/clound.png" mode="scaleToFill" />
                                            服务器 <span
                                                style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">113</span>
                                        </uni-col>
                                        <uni-col :span="8"
                                            style="font-size: 30rpx;display: flex;align-items: center;color: #666;padding-top: 16rpx;justify-content: space-around;">
                                            <image style="height: 66rpx;width: 66rpx;"
                                                src="/static/images_new/clound.png" mode="scaleToFill" />
                                            虚拟机 <span
                                                style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">114</span>
                                        </uni-col>
                                        <uni-col :span="8"
                                            style="font-size: 30rpx;display: flex;align-items: center;color: #666;padding-top: 16rpx;justify-content: space-around;">
                                            <image style="height: 66rpx;width: 66rpx;"
                                                src="/static/images_new/clound.png" mode="scaleToFill" />
                                            云平台 <span
                                                style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">4</span>
                                        </uni-col>
                                        <uni-col :span="8"
                                            style="font-size: 30rpx;display: flex;align-items: center;color: #666;padding-top: 16rpx;justify-content: space-around;">
                                            <image style="height: 66rpx;width: 66rpx;"
                                                src="/static/images_new/clound.png" mode="scaleToFill" />
                                            应用服务 <span
                                                style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">14</span>
                                        </uni-col>
                                    </uni-row>
                                </view>

                            </view>
                        </view>
                        <view v-show="item == '实时态势呈现'" style="background-color: #fff;padding-top: 26rpx;">
                            <alarmQueryAsset :isShowTabs="false" />
                        </view>
                    </scroll-view>
                </view>
            </swiper-item>
        </swiper>

    </view>
</template>

<script>
import * as echarts from 'echarts'
import alarmQueryAsset from '../asset/alarm_query_asset.vue'
export default {
    data() {
        return {
            tabs: [
                { title: '运行态势统计', content: '第一页内容' },
                { title: '实时态势呈现', content: '第二页内容' },
            ],
            activeIndex: 0,     // 当前选中索引
            itemWidth: 0,       // 单个 Tab 的宽度
            lineWidth: 0,
            scrollLeft: 0,     // 滚动条位置
            lineLeft: 0,
            currentDate: '2025-03-13',
            searchValue: null,

            tabItems: ["运行态势统计", "实时态势呈现"],
            typeValues: ["TODO", "TRACK", "JOIN"],
            currentTabIndex: 0,
            value: 0,
            range: [
                { value: 0, text: "告警总数" },
                { value: 1, text: "严重告警数" },
                { value: 2, text: "重要告警数" },
            ],
            option: {
                series: [{
                    type: 'gauge',
                    startAngle: 90,
                    endAngle: -270,
                    pointer: {
                        show: false
                    },
                    grid: {
                        left: 20,
                        right: 20,
                        bottom: 15,
                        top: 10,
                        containLabel: true
                    },
                    progress: {
                        show: true,
                        overlap: false,
                        roundCap: true,
                        clip: false,
                        itemStyle: {
                            // borderWidth: 1,
                            // borderColor: '#464646'
                        }
                    },
                    axisLine: {

                        lineStyle: {
                            width: 6
                        }
                    },
                    splitLine: {
                        show: false,
                        distance: 0,
                        length: 10
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        show: false,
                        distance: 50
                    },
                    data: [
                        {
                            value: 66,
                            detail: {
                                offsetCenter: ['0%', '0%']
                            }
                        }
                    ],
                    detail: {
                        // width: 50,
                        // height: 14,
                        fontSize: 13,
                        //color: 'auto',
                        // borderColor: 'auto',
                        // borderRadius: 20,
                        // borderWidth: 1,
                        formatter: '{value}%'
                    }
                }]
            },
            optionBrokenLine: {
                xAxis: {
                    type: 'category',
                    data: ['03-11', '03-13', '03-16', '03-19', '03-20', '03-22', '03-26']
                },
                yAxis: {
                    type: 'value'
                },
                series: [{
                    data: [150, 250, 190, 266, 333, 290, 366],
                    type: 'line',
                    smooth: true,
                    areaStyle: {}

                }]
            },
        }
    },
    components: {
        alarmQueryAsset
    },
    onLoad(page) {

    },
    computed: {
        // 底部滑动条样式
        lineStyle() {
            return {
                width: `${this.lineWidth}px`, // 改用 lineWidth
                transform: `translateX(${this.activeIndex * this.itemWidth + (this.itemWidth - this.lineWidth) / 2}px)`, // 居中计算
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                // #ifdef APP-PLUS
                width: '226rpx',
                transform: `translateX(${this.activeIndex * 226}rpx)`,
                marginLeft: '26rpx',
                margin: ' 0 auto',
                // #endif
            }
        },

    },
    mounted() {
        this.init();
        this.calcTabWidth();
    },
    onBackPress() {

    },
    methods: {
        async init() {
            this.$refs.chartRef02[0].init(echarts, chart => {
                let option = {
                    tooltip: {
                        trigger: 'item'
                    },
                    legend: {
                        orient: 'vertical',      // 改为垂直方向排列
                        left: '6rpx',        // 图例靠左
                        top: 'center',           // 垂直居中
                        align: 'left',           // 文字左对齐（避免图标与文字间距过大）
                        itemGap: 20              // 增大图例项间距
                    },
                    series: [
                        {
                            name: '访问来源',
                            type: 'pie',
                            center: ['66%', '50%'],  // 圆心向右偏移（留出左侧给图例）
                            radius: ['40%', '70%'],  // 适当缩小半径
                            avoidLabelOverlap: false,
                            label: {
                                show: false,
                                position: 'center'
                            },
                            emphasis: {
                                label: {
                                    show: true,
                                    fontSize: '20',
                                    fontWeight: 'bold'
                                }
                            },
                            labelLine: {
                                show: false
                            },
                            data: [
                                { value: 1048, name: '端口宏告警' },
                                { value: 735, name: 'ping丢包告警' },
                                { value: 580, name: '其他告警' },
                                { value: 366, name: '不可达告警' },
                            ]
                        }
                    ]
                }
                chart.setOption(option);

            });
        },

        // 统一计算逻辑（跨平台）
        calcTabWidth() {
            // #ifdef APP-PLUS
            const dom = uni.requireNativePlugin('dom')
            console.log(this.$refs[`tabItem${this.activeIndex}`][0])
            dom.getComponentRect(this.$refs[`tabItem${this.activeIndex}`][0], res => {
                console.log('APP端元素尺寸:', res)
                if (res?.size?.width) {
                    this.itemWidth = res.size.width
                    this.lineWidth = res.size.width * 0.8
                    // 测试用提示
                    // uni.showToast({ title: `宽度:${this.itemWidth}`, icon: 'none' })
                } else {
                    // uni.showToast({ title: '获取宽度失败', icon: 'none' })
                }
            })
            // #endif

            // #ifndef APP-PLUS
            // 非原生环境通用逻辑
            const query = uni.createSelectorQuery().in(this)
            query.select('.tab-item').boundingClientRect(res => {
                if (res) {
                    this.itemWidth = res.width
                    this.lineWidth = res.width * 0.8
                }
            }).exec()
            // #endif
        },

        // 调整滚动位置（跨平台兼容）
        adjustScrollPosition() {
            const systemInfo = uni.getSystemInfoSync();
            console.log(systemInfo)
            let offset = this.activeIndex * this.itemWidth - systemInfo.windowWidth / 2 + this.itemWidth / 2
            // #ifdef APP-PLUS
            const dom = uni.requireNativePlugin('dom');
            const el = this.$refs.tabLine
            dom.scrollToElement(el, {
                offset: 0
            })
            // #endif
            // #ifdef APP-PLUS
            // 原生环境增加安全偏移
            offset = Math.max(0, offset - 8)
            // 原生滚动控制
            this.$nextTick(() => {
                this.$refs.tabScroll.setScrollLeft({
                    scrollLeft: offset,
                    duration: 300
                })
            })
            // #else
            // 非原生环境直接赋值
            this.scrollLeft = Math.max(0, offset)
            // #endif

            // 滑动条位置计算（跨平台通用）
            this.lineLeft = this.activeIndex * this.itemWidth + (this.itemWidth - this.lineWidth) / 2
        },

        // 点击切换 Tab
        switchTab(index) {
            console.log(index);
            this.currentTabIndex = index;
            this.activeIndex = index;
            this.adjustScrollPosition()
        },

        // 滑动切换回调
        onSwiperChange(e) {
            console.log(e.detail.current);
            this.activeIndex = e.detail.current
            this.currentTabIndex = e.detail.current;
            this.adjustScrollPosition()
        },
    }
}
</script>

<style lang="scss" scoped>
.correlation_statistics {
    .item {
        height: 100rpx;
        // border: 2rpx solid #000;
        border-radius: 16rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0 6rpx;
    }
}

/* Tab 栏样式 */
.tab-scroll {
    width: 100%;
    height: 44px;
    background: #fff;
    border-bottom: 1px solid #eee;

}

.tab-bar {
    position: relative;
    height: 100%;
    white-space: nowrap;
}

.tab-item {
    display: inline-block;
    height: 44px;
    line-height: 44px;
    padding: 0 26rpx;
    font-size: 29rpx;
    color: #666;
    transition: color 0.3s;
}

.tab-item.active {
    color: #007AFF;
    font-weight: bold;
}

/* 底部滑动条 */
.tab-line {
    position: absolute;
    bottom: 0;
    height: 3px;
    background: #007AFF;
    transition: transform 0.3s ease;
}

/* 内容区域 */
swiper {
    flex: 1;
    height: calc(116vh - 44px);
    background-color: #fff;
}

.content {
    height: 100%;
    // padding: 20px;
    background: #f5f5f5;
}

/* 隐藏滚动条（全平台通用） */
// .tab-scroll {
// 	overflow: hidden !important;
// }

/* 针对 H5 的隐藏方式 */
.tab-scroll ::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    color: transparent !important;
}
</style>