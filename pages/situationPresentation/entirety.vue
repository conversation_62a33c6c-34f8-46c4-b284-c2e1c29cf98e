<template>
    <view class="container">
        <view>
            <view style="padding:16rpx">整体统计</view>
            <uni-row style="margin-bottom: 26rpx;">
                <uni-col :span="6">
                    <image style="width: 126rpx;height:126rpx;" src="/static/images_new/earth.png" mode="scaleToFill" />
                </uni-col>
                <uni-col :span="6">
                    <view style="font-size: 53rpx;font-weight: 500;color: #17a9ff;">234</view>
                    <view style="font-size: 30rpx;color: #666;">资源总数</view>
                </uni-col>
                <uni-col :span="6">
                    <view style="font-size: 53rpx;font-weight: 500;color: #17a9ff;">234</view>
                    <view style="font-size: 30rpx;color: #666;">纳入监控</view>
                </uni-col>
                <uni-col :span="6">
                    <view style="font-size: 53rpx;font-weight: 500;color: #17a9ff;">100%</view>
                    <view style="font-size: 30rpx;color: #666;">监控覆盖率</view>
                </uni-col>
            </uni-row>
            <uni-row>
                <uni-col :span="8"
                    style="font-size: 30rpx;display: flex;align-items: center;color: #666;justify-content: space-around;">
                    <image style="height: 66rpx;width: 66rpx;" src="/static/images_new/server.png" mode="scaleToFill" />
                    服务器 <span style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">113</span>
                </uni-col>
                <uni-col :span="8"
                    style="font-size: 30rpx;display: flex;align-items: center;color: #666;justify-content: space-around;">
                    <image style="height: 66rpx;width: 66rpx;" src="/static/images_new/server.png" mode="scaleToFill" />
                    中间件 <span style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">13</span>
                </uni-col>
                <uni-col :span="8"
                    style="font-size: 30rpx;display: flex;align-items: center;color: #666;justify-content: space-around;">
                    <image style="height: 66rpx;width: 66rpx;" src="/static/images_new/clound.png" mode="scaleToFill" />
                    服务器 <span style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">113</span>
                </uni-col>
                <uni-col :span="8"
                    style="font-size: 30rpx;display: flex;align-items: center;color: #666;padding-top: 16rpx;justify-content: space-around;">
                    <image style="height: 66rpx;width: 66rpx;" src="/static/images_new/clound.png" mode="scaleToFill" />
                    虚拟机 <span style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">114</span>
                </uni-col>
                <uni-col :span="8"
                    style="font-size: 30rpx;display: flex;align-items: center;color: #666;padding-top: 16rpx;justify-content: space-around;">
                    <image style="height: 66rpx;width: 66rpx;" src="/static/images_new/clound.png" mode="scaleToFill" />
                    云平台 <span style="margin-left: 6rpx;font-size: 36rpx;color: #17a9ff;">4</span>
                </uni-col>
            </uni-row>
        </view>
        <view>
            <view style="margin-top: 36rpx;padding-bottom: 26rpx;">整体架构</view>
            <view
                style="width: 100%;height: 333rpx;border: 2rpx dashed #17a9ff;padding: 16rpx;box-sizing: border-box;border-radius: 16rpx;margin-bottom: 26rpx;">
                <view style="padding-bottom: 16rpx;">应用层服务</view>
                <view class="app_server">
                    <view>应用1</view>
                    <view>应用2</view>
                    <view>应用3</view>
                    <view>应用4</view>
                    <view>应用5</view>
                    <view>应用6</view>
                </view>
            </view>
            <view
                style="width: 100%;height: 333rpx;border: 2rpx dashed #17a9ff;padding: 16rpx;box-sizing: border-box;border-radius: 16rpx;margin-bottom: 26rpx;">
                <view style="padding-bottom: 16rpx;">中间件层</view>
                <view>云平台：xx套</view>
            </view>
            <view
                style="width: 100%;height: 300rpx;border: 2rpx dashed #17a9ff;padding: 16rpx;box-sizing: border-box;border-radius: 16rpx;margin-bottom: 26rpx;">
                <view style="padding-bottom: 16rpx;">应用层服务</view>
                <view class="basice_server">
                    <view>机房1</view>
                    <view>机房2</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    components: {
    },
    data() {
        return {
        }
    },

    // 页面加载
    onLoad(page) {
    },

    methods: {},
    watch: {
    }
}
</script>

<style lang="scss" scoped>
.container {
    background-color: #fff;
    padding: 26rpx;
    height: 100vh;
}

.app_server {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16rpx;

    view {
        height: 100rpx;
        border: 2rpx dashed #17a9ff;
        border-radius: 26rpx;
        line-height: 100rpx;
        text-align: center;
        color: #17a9ff;
        box-shadow: 3rpx 3rpx 6rpx;
    }
}

.basice_server {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
    view {
        height: 166rpx;
        border: 2rpx dashed #17a9ff;
        border-radius: 26rpx;
        line-height: 166rpx;
        text-align: center;
        color: #17a9ff;
        box-shadow: 3rpx 3rpx 6rpx;
    }
}
</style>