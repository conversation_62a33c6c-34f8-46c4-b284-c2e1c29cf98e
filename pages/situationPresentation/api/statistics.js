import axios from "/common/axios.js"


// 用户数量统计
export const userOverviewUserCount = () => {
	return axios.get(`/userOverview/userCount`)
}

// 使用情况统计
export const userOverviewUsage = () => {
	return axios.get(`/userOverview/usage`)
}

// 热门应用
export const userOverviewHotApp = (p) => {
	return axios.get(`/userOverview/hotApp?day=${p.days}`)
}

// 应用调用情况统计
export const docAppTotalStats = () => {
	return axios.get(`/docApp/totalStats?days=0`)
}

// 调用时段统计
export const docAppTimeStats = (p) => {
	return axios.get(`/docApp/timeStats?days=${p.days}`)
}

// 部门Top5
export const docAppDeptTop5 = (p) => {
	return axios.get(`/docApp/deptTop5?days=${p.days}`)
}

