<template>
	<view class="deal-todo-list" :class="{ 'deal-todo-list-dark': theme }">
		<view class="main-form">
			<!-- 流程form -->
			<flow-form :key="formKey" ref="formForm" :model="formMain" :component-groups="componentGroups">
				<template #bottom>
					<uni-section style="padding: 0 16rpx;" title="工单详情" type="line">
						<uni-easyinput :disabled="isDraft != '拟稿'" v-model="valiFormData.detail" placeholder="工单描述信息"
							autoHeight :maxlength="1000" type="textarea"></uni-easyinput>

						<uni-forms-item class="uni-forms-item__content_file" label="附件">
							<view style="padding-bottom: 16rpx;color: #aaa;width: 100px;">{{ isDraft == '拟稿' ? '可上传多个文件'
								:
								''}}</view>
							<upload-demo :isDraft="isDraft" :businessId="valiFormData.id" type="file"></upload-demo>
						</uni-forms-item>
					</uni-section>

					<uni-section v-if="!nextNodeIsEnd" title="派发对象" type="line">
						<uni-group>
							<uni-forms-item label-width="130px" label="组织机构" name="deptName">
								<ba-tree-picker :selectedData="valiFormData.selectedData" ref="treePicker"
									:multiple='false' @select-change="selectChange" title="选择组织机构" :localdata="listData"
									valueKey="deptId" textKey="deptName" childrenKey="children" />
								<view @click="showPicker">
									<uni-easyinput :clearable="false" @focus="handleFocus"
										v-model="valiFormData.deptName" placeholder="请选择组织机构" />
								</view>

							</uni-forms-item>
							<uni-forms-item label="受理人">
								<pop-select v-if="participantOptionsView" @update:modelValue="userNamesDataUpdate"
									v-model="valiFormData.newuserNames" placeholder="请选择受理人" :multiple="true"
									:options="participantOptions" value-to-string>
									<template #label="option">
										<view>{{ option.realName }}</view>
									</template>
								</pop-select>
							</uni-forms-item>
						</uni-group>
					</uni-section>

					<button style="float: right;margin: 16rpx;margin-top: 10rpx;" @click="viewFlowChart" type="primary"
						size="mini">查看流程图</button>
					<uni-section title="最新进度" type="line">
						<uni-group>
							<flow-steps class="process-steps" :options="processLinks" active-color="#007AFF"
								:active="processLinks.length - 1" direction="column">
								<template #item="{ item, index, isLastItem }">
									<!-- 第一个创建环节不显示 -->
									<view class="item">
										<view class="step-title" style="font-weight: bold;color: #10172A;"
											v-text="item.nodeName || item.name || '未知环节'"></view>
										<view class="col">
											<view>派单人：</view>
											<view style="color: #3C5176;">{{ item.approverName }}</view>
										</view>
										<view v-if="item.message != '_' && item.message" class="col">
											<view>处理说明：</view>
											<view style="
												color: #3C5176;
												white-space: pre-wrap;
												word-break: break-all;
												width: 60vw;
												overflow: hidden;
												">
												{{ item.message || '-' }}
											</view>
										</view>
										<view class="col">
											<view>派单时间：</view>
											<view style="color: #3C5176;">{{ item.updateTime }}</view>
										</view>
									</view>
								</template>
							</flow-steps>
						</uni-group>
					</uni-section>
				</template>
			</flow-form>
		</view>
		<uni-row class="main-form-bottom" :gutter="20"
			style="background-color: #fff; margin: 0 auto; position: absolute;bottom: 10rpx;width: 100%;padding: 10rpx;box-sizing: border-box;">
			<uni-col :span="canReject ? 12 : (valiFormData.nodeName == '拟稿' ? 12 : 24)">
				<button type="primary" @click="openDialog" :loading="submitLoading">
					{{ nextNodeIsEnd ? '归档' : '提交' }}
				</button>
			</uni-col>
			<!-- 添加撤销工单 -->
			<uni-col v-if="valiFormData.nodeName == '拟稿'"
				:span="canReject ? 12 : (valiFormData.nodeName == '拟稿' ? 12 : 0)">
				<button type="primary" @click="cancelOpenDialog_fn">撤单</button>
			</uni-col>
			<uni-col v-if="canReject" :span="canReject ? 12 : 0">
				<button type="primary" @click="backOpenDialog_fn">退回</button>
			</uni-col>
		</uni-row>

		<uni-popup class="custom-dialog-submit" ref="dialog" type="dialog">
			<view class="custom-dialog">
				<view class="title">{{ dialogTitle }}</view>
				<view class="content">
					<textarea v-model="valiFormData.easyDeac" class="uni-textarea"
						style="padding: 10rpx;box-sizing: border-box;border: 2rpx solid #eee;margin: 10rpx;"
						placeholder-style="color:#eee" placeholder="简单描述" />
				</view>
				<view class="footer">
					<button type="default" @click="close">取消</button>
					<button type="primary" @click="confirm">确定</button>
				</view>
			</view>
		</uni-popup>
		<uni-popup class="custom-dialog-cancelOpenDialog" ref="cancelOpenDialog" type="dialog">
			<view class="custom-dialog-new">
				<view class="title" style="padding-bottom: 23rpx;">提示</view>
				<view class="content">
					关闭表单后，所有填写内容将被永久删除且无法恢复，请确认是否继续？
				</view>
				<view class="footer" style="margin-top: 13px;">
					<button type="default" @click="cancelClose">取消</button>
					<button type="primary" @click="cancelConfirm">确认关闭</button>
				</view>
			</view>
		</uni-popup>

		<uni-popup class="custom-dialog-backDialog" ref="backDialog" type="dialog">
			<view class="custom-dialog">
				<view class="title">工单退回</view>
				<view class="content">
					<textarea v-model="valiFormData.easyDeac" class="uni-textarea"
						style="padding: 10rpx;box-sizing: border-box;border: 2rpx solid #eee;margin: 10rpx;"
						placeholder-style="color:#eee" placeholder="简单描述" />
				</view>
				<view class="footer">
					<button type="default" @click="backClose">取消</button>
					<button type="primary" @click="backConfirm">确定</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
import { ref, reactive, watch, nextTick, onMounted } from 'vue';
import FlowForm from "./flow-form.vue";
import FlowSteps from "./flow-steps.vue";
import list from "./list.js";
import flow from "./flow.js";
import {
	workOrderSaveBeforeFlow, getProcSelects, workOrderGetById,
	workOrderListWorkOrderLevel, queryDepts, sysmanageUsersList,
	taskListDone, flowExecuteSkipFlow, flowExecuteCloseFlow
} from "./api/index.js";
import baTreePicker from "/components/ba-tree-picker/ba-tree-picker.vue";
import UploadDemo from "/components/xe-upload_1/components/UploadDemo.vue";
import LFile from "/components/l-file_1-3/pages/index/index.vue";

// 引入mixins中的方法
const listMixin = list;
const flowMixin = flow;

// 定义响应式数据
const dialogTitle = ref("处理说明");
const dialogVisible = ref(false);
const topActive = ref(0);
const list1 = ref([
	{ desc: '周毛毛\n2018-11-11', title: '创建', },
	{ title: '告警处理', desc: '周毛毛\n2018-11-12' },
	{ title: '告警确认', desc: '周毛毛\n2018-11-13' },
	{ title: '归档', desc: '周毛毛\n2018-11-14' }
]);
const procKey = ref(null);
const mainId = ref(null);
const taskDefKey = ref(null);
const taskId = ref(null);

// 表单key
const formKey = ref(1);
// 表单模型对象
const formMain = ref({});
// 流程模型定义信息
const processMain = ref({});
// 组件分组
const componentGroups = ref([]);

// 受理人
const taskParticipant = ref({});
// 抄送人
const taskParticipantcopy = ref({});
// 选择分支
const nextActivity = ref({});
// 基本信息
const baseParams = ref({});

// 环节记录
const processLinks = ref([]);
const active = ref(1);

const canReject = ref(false);
const nextNodeIsEnd = ref(false);
const valiFormData = reactive({
	code: '',
	title: '',
	creatorName: '',
	orderLevel: "",
	detail: "",
	selectedData: [],
	deptName: "",
	deptId: "",
	userNames: "",
	userIds: "",
	easyDeac: "",
	newuserNames: "",
	id: "", // 添加id属性，因为在模板中使用了valiFormData.id
	nodeName: "" // 添加nodeName属性，因为在模板中使用了valiFormData.nodeName
});
const listData = ref([]);
const participantOptions = ref([]);
const participantOptionsView = ref(true);

const businessId = ref('');
const businessType = ref("");

// 确认当前状态是不是拟稿
const isDraft = ref("");

const levelListArray = ref([]);
const theme = ref(false);
const submitLoading = ref(false);

// 引用
const formForm = ref(null);
const treePicker = ref(null);
const dialog = ref(null);
const cancelOpenDialog = ref(null);
const backDialog = ref(null);

// 监听deptId变化
watch(() => valiFormData.deptId, (newVal, oldVal) => {
	console.log('deptId changed: ', newVal, oldVal);
	valiFormData.selectedData = [newVal];
	getSysmanageUsersList({
		"deptId": valiFormData.deptId,
		"pageNo": 1,
		"pageSize": 100000
	});
	participantOptionsView.value = false;
	nextTick(() => {
		participantOptionsView.value = true;
		valiFormData.userIds = '';
		valiFormData.newuserNames = '';
		console.log("valiFormData.userIds", valiFormData.userIds);
	});
}, { deep: true });

// 监听主题变化
watch(() => theme.value, (newVal) => {
	uni.setStorageSync('theme', newVal);
	if (newVal) {
		uni.setNavigationBarColor({
			frontColor: '#ffffff',
			backgroundColor: '#2b2b2b',
		});
		uni.setTabBarStyle({
			backgroundColor: '#2b2b2b',
			color: '#ffffff',
			selectedColor: '#fff'
		});
	} else {
		uni.setNavigationBarColor({
			frontColor: '#000000',
			backgroundColor: '#ffffff',
		});
		uni.setTabBarStyle({
			backgroundColor: '#ffffff',
			color: '#000000',
			selectedColor: '#000'
		});
	}
});

// 方法
const handleFocus = () => {
	uni.hideKeyboard();
	nextTick(() => {
		uni.hideKeyboard();
	});
};

const userNamesDataUpdate = (userid, username) => {
	console.log(userid, username);
	valiFormData.userNames = username;
	valiFormData.userIds = userid;
};

// 显示选择器
const showPicker = () => {
	treePicker.value._show();
};

const getSysmanageUsersList = (params) => {
	sysmanageUsersList(params).then(res => {
		let list = [];
		if (res?.data?.length > 0) {
			res.data.forEach(item => {
				list.push({
					...item,
					label: item.realName,
					value: item.userId
				});
			});
		}
		console.log(list);
		participantOptions.value = list;
	});
};

//监听选择（ids为数组）
const selectChange = (ids, names) => {
	console.log(ids, names);
	valiFormData.selectedData = ids;
	valiFormData.deptName = names;
	valiFormData.deptId = ids[0];
};

const viewFlowChart = () => {
	// viewFlowChart 去往viewFlowChart页
	uni.navigateTo({
		url: `/pages/list/viewFlowChart?instanceId=${valiFormData.instanceId}`
	});
};

const back = () => {
	uni.navigateBack({
		delta: 1
	});
};

const backAndRefrush = () => {
	// #ifdef H5
	// H5平台可以使用getCurrentPages
	try {
		let pages = getCurrentPages(); // 当前页面
		let beforePage = pages[pages.length - 2]; // 前一个页面
		uni.navigateBack({
			success: function () {
				typeof (beforePage.refresh) == 'function' && beforePage.refresh();
			}
		});
	} catch (error) {
		console.warn('getCurrentPages failed on H5:', error);
		uni.navigateBack();
	}
	// #endif

	// #ifdef APP-PLUS || MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
	// 移动端和小程序平台直接返回，不调用前一页面的refresh方法
	uni.navigateBack();
	// #endif
};

const initFlow = (page) => {
	queryDepts().then((res) => {
		listData.value = res.data;
		console.log(listData.value);
	});

	workOrderListWorkOrderLevel().then(res => {
		levelListArray.value = res.data;
		res.data.forEach(item => {
			item.text = item.label;
		});
		componentGroups.value = [
			{
				"appComponent": [
					{
						"field": "code",
						"name": "工单号",
						"uid": "code",
						"component": {
							"type": "Input",
							"attrs": {
								"readonly": 1,
								"hidden": 0,
								"required": 0
							},
							"props": {
								"placeholder": "请输入工单号"
							}
						}
					},
					{
						"field": "title",
						"name": "标题",
						"uid": "title",
						"component": {
							"type": "Input",
							"attrs": {
								"readonly": isDraft.value == '拟稿' ? 0 : 1,
								"hidden": 0,
								"required": 1
							},
							"props": {
								"placeholder": "请输入标题"
							}
						}
					},
					{
						"field": "creatorName",
						"name": "创建人",
						"uid": "creatorName",
						"component": {
							"type": "Input",
							"attrs": {
								"readonly": 1,
								"hidden": 0,
								"required": 0
							},
							"props": {
								"placeholder": "请输入创建人"
							}
						}
					},
					{
						"field": "deptName",
						"name": "创建人部门名称",
						"uid": "deptName",
						"component": {
							"type": "Input",
							"attrs": {
								"readonly": 1,
								"hidden": 0,
								"required": 0
							},
							"props": {
								"placeholder": "请输入创建人部门名称"
							}
						}
					},
					{
						"field": isDraft.value == '拟稿' ? "orderLevel" : "orderLevelName",
						"name": "工单级别",
						"uid": isDraft.value == '拟稿' ? "orderLevel" : "orderLevelName",
						"component": {
							"type": isDraft.value == '拟稿' ? "Select" : "input",
							"attrs": {
								"readonly": isDraft.value == '拟稿' ? 0 : 1,
								"hidden": 0,
								"required": 0
							},
							"props": {
								"placeholder": "请选择工单级别",
							},
							// 定义静态选项
							"options": res.data
						},
					},
				],
				"groupName": "基本信息"
			},
		];
	});

	// 获取可以操作信息
	getProcSelects({
		definitionId: page.definitionId,
		nowNodeCode: page.nowNodeCode,
	}).then(res => {
		canReject.value = res.data.canReject;
		nextNodeIsEnd.value = res.data.nextNodeIsEnd;
	});

	workOrderGetById(page.id).then(res => {
		console.log("workOrderGetById",res.data);
		const level = levelListArray.value;
		level.forEach(item => {
			if (item.value == res.data.orderLevel) {
				res.data.orderLevelName = item.label;
			}
		});
		formMain.value = res.data;
		Object.assign(valiFormData, res.data);

		taskListDone(res.data.instanceId).then(res => {
			let arr = res?.data?.reverse();
			processLinks.value = arr;
		});
	});

	getSysmanageUsersList({
		"deptId": "-1",
		"pageNo": 1,
		"pageSize": 100000
	});
};

const openDialog = () => {
	const currentFormData = formForm.value?.model;
	if (valiFormData?.deptName?.length == 0) {
		uni.showToast({
			title: '请选择受理人所属部门',
			duration: 2000,
			icon: 'none',
		});
		return false;
	}
	if (currentFormData.title == '' || !currentFormData.title) {
		uni.showToast({
			title: '请填写标题',
			icon: 'none'
		});
		return;
	}
	if (nextNodeIsEnd.value || (valiFormData.newuserNames && valiFormData.newuserNames.length > 0)) {
	} else {
		uni.showToast({
			title: '请选择受理人',
			icon: 'none'
		});
		return;
	}
	valiFormData.easyDeac = "";
	dialog.value.open();  // 动态打开对话框
};

const close = () => {
	dialog.value.close(); // 关闭对话框
};

const confirm = () => {
	// 处理确认逻辑
	completeTask('PASS');
};

const cancelClose = () => {
	cancelOpenDialog.value.close(); // 关闭对话框
};

const cancelConfirm = () => {
	flowExecuteCloseFlow({
		businessId: businessId.value,
		businessType: businessType.value,
		"skipType": "NONE"
	}).then(res => {
		if (res.status == 0) {
			uni.showToast({
				title: '流程关闭成功',
				icon: 'none'
			});
			setTimeout(() => {
				uni.navigateBack();
			}, 1000);
		} else {
			uni.showToast({
				title: '流程关闭失败',
				icon: 'none'
			});
		}
	});
};

const cancelOpenDialog_fn = () => {
	cancelOpenDialog.value.open();  // 动态打开对话框
};

const backOpenDialog_fn = () => {
	const currentFormData = formForm.value?.model;
	if (currentFormData.title == '' || !currentFormData.title) {
		uni.showToast({
			title: '请填写标题',
			icon: 'none'
		});
		return;
	}
	if (valiFormData?.deptName?.length == 0) {
		uni.showToast({
			title: '请选择受理人所属部门',
			duration: 2000,
			icon: 'none',
		});
		return false;
	}
	if (valiFormData.newuserNames && valiFormData.newuserNames.length > 0) {
	} else {
		uni.showToast({
			title: '请选择受理人',
			icon: 'none'
		});
		return;
	}
	valiFormData.easyDeac = "";
	backDialog.value.open();  // 动态打开对话框
};

const backClose = () => {
	backDialog.value.close(); // 关闭对话框
};

const backConfirm = () => {
	// 处理确认逻辑
	completeTask('REJECT');
};

const completeTask = (skipType) => {
	if (submitLoading.value) return;

	let formFormInstance = formForm.value;
	formFormInstance.validate().then(() => {
		uni.showLoading({
			title: "正在处理中...",
			icon: "loading"
		});
		submitLoading.value = true;
		const requestObject = {
			"skipType": skipType,
			"businessId": businessId.value,
			"businessType": businessType.value,
			"message": valiFormData.easyDeac,
			"userIds": valiFormData.newuserNames,
			"nextNodeIsEnd": nextNodeIsEnd.value
		};
		const postFormData = {
			...valiFormData,
			...formMain.value,
			detail: valiFormData.detail,
			orderLevelName: '' // 添加 orderLevelName 属性
		};

		levelListArray.value.forEach(item => {
			console.log(item);
			if (item.value == postFormData.orderLevel) {
				postFormData.orderLevelName = item.label;
				return;
			}
		});
		console.log(postFormData);

		workOrderSaveBeforeFlow(postFormData).then(res => {
			if (res.data) {
				flowExecuteSkipFlow(requestObject).then(res => {
					let resData = res.data;
					console.log("resData", resData);
					if (resData.length == '0') {
						uni.showToast({
							title: "操作成功"
						});
						backAndRefrush();
					} else {
						uni.showToast({
							title: res.data,
							icon: "none"
						});
						console.error("res", res);
					}
				}).catch(err => {
					console.error(err);
					uni.showToast({
						title: "操作失败"
					});
				}).finally(() => {
					submitLoading.value = false;
				});
			} else {
				uni.showToast({
					title: "操作失败"
				});
				submitLoading.value = false;
			}
		}).catch(err => {
			console.error(err);
			uni.showToast({
				title: "操作失败"
			});
		}).finally(() => {
			submitLoading.value = false;
		});
	}).catch(data => {
		console.log(data);
		let msg = "校验未通过";
		if (Array.isArray(data)) {
			let firstField = data[0].key;
			formFormInstance.scrollPropertyToView(firstField);
		}
		uni.showToast({
			title: msg,
			duration: 1000
		});
	});
};

// 页面加载 - 兼容uni-app页面生命周期
const onLoad = (page) => {
	// 存储页面参数供其他平台使用
	pageOptions.value = page || {};

	businessId.value = page.id;
	businessType.value = page.businessType;
	let record = listMixin.getCurrent ? listMixin.getCurrent() : {};
	console.log("record", record);
	let { procKey: procKeyVal, mainId: mainIdVal, taskDefKey: taskDefKeyVal, taskId: taskIdVal } = record;
	uni.setNavigationBarTitle({
		title: page.nodeName + " - 处理工单"
	});

	if (page.nodeName == '拟稿') {
		isDraft.value = '拟稿';
	} else {
		isDraft.value = '不是拟稿';
	}

	procKey.value = procKeyVal;
	mainId.value = mainIdVal;
	taskDefKey.value = taskDefKeyVal;
	taskId.value = taskIdVal;

	initFlow(page);

	theme.value = uni.getStorageSync('theme') || false;
	if (theme.value) {
		uni.setNavigationBarColor({
			frontColor: '#ffffff',
			backgroundColor: '#2b2b2b',
		});
		uni.setTabBarStyle({
			backgroundColor: '#2b2b2b',
			color: '#ffffff',
			selectedColor: '#fff'
		});
	} else {
		uni.setNavigationBarColor({
			frontColor: '#000000',
			backgroundColor: '#ffffff',
		});
		uni.setTabBarStyle({
			backgroundColor: '#ffffff',
			color: '#000000',
			selectedColor: '#000'
		});
	}
};

const onShow = () => {
	theme.value = uni.getStorageSync('theme') || false;
	if (theme.value) {
		uni.setNavigationBarColor({
			frontColor: '#ffffff',
			backgroundColor: '#2b2b2b',
		});
		uni.setTabBarStyle({
			backgroundColor: '#2b2b2b',
			color: '#ffffff',
			selectedColor: '#fff'
		});
	} else {
		uni.setNavigationBarColor({
			frontColor: '#000000',
			backgroundColor: '#ffffff',
		});
		uni.setTabBarStyle({
			backgroundColor: '#ffffff',
			color: '#000000',
			selectedColor: '#000'
		});
	}
};

// 页面参数存储
const pageOptions = ref({});

// 获取页面参数的跨平台方法
const getPageOptions = () => {
	// #ifdef H5
	// H5平台可以使用getCurrentPages
	try {
		const pages = getCurrentPages();
		const currentPage = pages[pages.length - 1];
		return currentPage.options || {};
	} catch (error) {
		console.warn('getCurrentPages failed on H5:', error);
		return pageOptions.value;
	}
	// #endif

	// #ifndef H5
	// 移动端和小程序平台使用存储的参数
	return pageOptions.value;
	// #endif
};

// 添加 onMounted 生命周期钩子
onMounted(() => {
	// 获取页面参数
	const options = getPageOptions();
	console.log('onMounted', options.id);
	if (options.id) {
		onLoad(options);
	}
	onShow();
});

// 暴露给模板的方法
defineExpose({
	onLoad,
	onShow,
	handleFocus,
	userNamesDataUpdate,
	showPicker,
	selectChange,
	viewFlowChart,
	openDialog,
	close,
	confirm,
	cancelClose,
	cancelConfirm,
	cancelOpenDialog: cancelOpenDialog_fn,
	backOpenDialog: backOpenDialog_fn,
	backClose,
	backConfirm
});
</script>
<style lang="scss" scoped>
.uni-forms-item__content_file {
	:deep(.uni-forms-item__content) {
		width: 66px;
	}
}
</style>
<style lang="scss" scoped>
.deal-todo-list {

	.main-form {
		/* #ifndef H5 */
		height: calc(100vh - 60px);
		/* #endif */
		/* #ifdef H5 */
		height: calc(100vh - 100px);
		/* #endif */
		overflow: auto;


		.process-steps {
			.item {
				.col {
					display: flex;
					margin: 10px 0;
				}
			}
		}
	}

	.uni-steps-Custom {
		:deep(.uni-steps__row) {
			display: flex;
			flex-direction: column-reverse;
		}

		:deep(.uni-steps__row-title) {
			padding: 13rpx;
			font-size: 29rpx;
		}
	}


	.custom-dialog {
		width: 79vw;
		background: #fff;
		border-radius: 66rpx;
		padding: 30rpx;

		.uni-textarea {
			width: auto;
			margin-top: 26rpx !important;
			border-radius: 16rpx;
		}
	}

	.custom-dialog-new {
		width: 79vw;
		background: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		padding-top: 43rpx;
		font-size: 16px;
		color: #333;

	}

	.title {
		width: 100%;
		text-align: center;
		font-size: 32rpx;
		font-weight: bold;
	}

	.footer {
		display: flex;
		justify-content: center;

		button {
			font-size: 26rpx;
			margin: 20rpx;
			padding: 0rpx 56rpx;
		}
	}
}

.deal-todo-list-dark {
	background: #2b2b2b;

	:deep(.uni-section) {
		background: #2b2b2b;

		span {
			color: #fff;
		}
	}

	:deep(.uni-group__content) {
		background: #2b2b2b;
	}

	:deep(.uni-forms-item--border) {
		border-top: 1px #aaa6a6 solid;
	}

	:deep(.uni-forms-item__content) {
		color: #ffffffd2;
	}

	.main-form {
		/* #ifndef H5 */
		height: calc(100vh - 60px);
		/* #endif */
		/* #ifdef H5 */
		height: calc(100vh - 100px);
		/* #endif */
		overflow: auto;

		.process-steps {
			.item {
				.col {
					display: flex;
					margin: 10px 0;
				}
			}
		}
	}

	.uni-steps-Custom {
		:deep(.uni-steps__row) {
			display: flex;
			flex-direction: column-reverse;
		}

		:deep(.uni-steps__row-title) {
			padding: 13rpx;
			font-size: 29rpx;
		}
	}

	.custom-dialog {
		width: 79vw;
		background: #fff;
		border-radius: 66rpx;
		padding: 30rpx;

		.uni-textarea {
			width: auto;
			margin-top: 26rpx !important;
			border-radius: 16rpx;
		}
	}

	.custom-dialog-new {
		width: 79vw;
		background: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		padding-top: 43rpx;
		font-size: 16px;
		color: #333;
	}

	.title {
		width: 100%;
		text-align: center;
		font-size: 32rpx;
		font-weight: bold;
	}

	.footer {
		display: flex;
		justify-content: center;

		button {
			font-size: 26rpx;
			margin: 20rpx;
			padding: 0rpx 56rpx;
		}
	}

	.custom-dialog-submit {
		background: #2b2b2b !important;
		color: #fff !important;

		.custom-dialog {
			background: #2b2b2b !important;
			color: #fff !important;
		}
	}

	.custom-dialog-cancelOpenDialog {
		background: #2b2b2b !important;

		.custom-dialog-new {
			background: #2b2b2b !important;
			color: #fff !important;
		}
	}

	.custom-dialog-backDialog {
		background: #2b2b2b !important;
		color: #fff !important;

		.custom-dialog {
			background: #2b2b2b !important;
			color: #fff !important;
		}
	}

	:deep(.uni-popup) {
		.uni-popup__wrapper {
			background: #2b2b2b !important;
		}
	}

	:deep(input) {
		background: #2b2b2b !important;
		color: #fff !important;
	}

	:deep(.uni-easyinput__content) {
		background: #2b2b2b !important;
		color: #fff !important;
	}

	:deep(.tree-dialog) {
		background: #2b2b2b !important;
		color: #fff !important;

		.tree-bar {
			background: #2b2b2b !important;
			color: #fff !important;

			.tree-bar-cancel {
				color: #fff !important;
			}
		}
	}

	:deep(.upload-wrap) {
		background: #2b2b2b !important;

		.btn-click {
			background: #2b2b2b !important;
			color: #fff !important;
		}
	}

	:deep(.file-line.btn-click) {
		background: #777676a4 !important;
		color: #fff !important;
	}

	:deep(.file-line) {
		background: #777676a4 !important;
		color: #fff !important;
	}

	:deep(.flow-steps__column-text) {
		color: #fff !important;

		.step-title {
			color: #fff !important;
		}

		.col uni-view {
			color: #ffffffc3 !important;
		}
	}

	.main-form-bottom {
		background: #2b2b2b !important;
		color: #fff !important;
		border-top: 1px solid #ffffff7b;
	}

	:deep(.uni-section__content-title) {
		color: #fff !important;
	}

	:deep(.uni-forms-item__label) {
		color: #fff !important;
	}

	:deep(.pop-select__input-text) {
		color: #fff !important;
	}
}
</style>