import {
	getUserPermissions,
	clearPermissionsCache,
	checkPermissionChanges
} from '../pages/user/api/permission.js';

const defaultCommonApps = ["my_todo_list", "my_tracked_list", "my_create_list", "my_notice_list", 'my_created_notice_list'];

// 应用功能权限控制 - 默认所有用户都有的基础权限
const defaultPermissions = [];

const assetManageApps = [
	// {
	//   name: 'create_asset',
	//   image: "/static/asset/create_asset.png",
	//   title: '创建资源',
	//   url: '/pages/asset/create_asset'
	// },
	{
		name: 'alarm_query_asset',
		image: "/static/asset/view_asset.png",
		title: '实时告警',
		url: '/pages/asset/alarm_query_asset'
	},
	{
		name: 'alarm_statistics_asset',
		image: "/static/asset/register_asset.png",
		title: '告警统计',
		url: '/pages/asset/alarm_statistics_asset'
	},
	{
		name: 'alarm_query_asset',
		image: "/static/asset/view_asset.png",
		title: '告警查询',
		url: '/pages/asset/alarm_query_asset?type=告警查询'
	},
	// {
	// 	name: 'register_asset',
	// 	image: "/static/asset/register_asset.png",
	// 	title: '登记资产',
	// 	url: '/pages/asset/alarm_query_asset?type=register'
	// },
	// {
	//   name: 'machine_room',
	//   image: "/static/asset/machine_room.png",
	//   title: '机房进入单',
	//   url: '/pages/asset/machine_room'
	// },
	// {
	// 	name: 'confirm_asset',
	// 	image: "/static/asset/confirm_asset.png",
	// 	title: '确认资产',
	// 	url: '/pages/asset/confirm_asset'
	// }
	// {
	//   name: 'unknown_asset',
	//   image: "/static/asset/unknown_asset.png",
	//   title: '未知资产',
	//   url: '/pages/asset/unknown_asset'
	// },
	// {
	//   name: 'corpse_asset',
	//   image: "/static/asset/corpse_asset.png",
	//   title: '僵尸资产',
	//   url: '/pages/asset/corpse_asset'
	// },
	// {
	//   name: 'overview_asset',
	//   image: "/static/asset/overview_asset.png",
	//   title: '资产概览',
	//   url: '/pages/asset/overview_asset'
	// },
];
// com.example.jdapphongmeng
const listManageApps = [
	// {
	// 	  name: 'todo',
	// 	  image: "/static/home/<USER>",
	// 	  title: '处理待办',
	// 	  url: '/pages/list/my_todo_list'
	// },
	{
		name: 'my_todo_list',
		image: "/static/list/my_todo_list.png",
		title: '我的待办',
		url: '/pages/list/my_list?index=0'
	},
	// {
	// 	  name: 'my_copy_list',
	// 	  image: "/static/list/my_copy_list.png",
	// 	  title: '抄送我的',
	// 	  url: '/pages/list/my_copy_list'
	// },
	{
		name: 'my_tracked_list',
		image: "/static/list/my_tracked_list.png",
		title: '我的已办',
		url: '/pages/list/my_list?index=1'
	},
	// {
	// 	  name: 'my_done_list',
	// 	  image: "/static/list/all_list.png",
	// 	  title: '我参与的',
	// 	  url: '/pages/list/my_list?index=2'
	// },
	{
		name: 'my_create_list',
		image: "/static/list/all_list.png",
		title: '创建工单',
		url: `/pages/list/draft_list?procKey=templatea&procName=新工单`
	},
	{
		name: 'work_order_statistics',
		image: "/static/list/all_list.png",
		title: '工单统计',
		url: `/pages/list/workOrderStatistics`
	},
	{
		name: 'my_notice_list',
		image: "/static/APP-new/view_notification_icon.svg",
		title: '查看通知',
		url: `/pages/list/notice?procKey=2`
	},
	{
		name: 'my_created_notice_list',
		image: "/static/APP-new/create_notification_icon.svg",
		title: '创建通知',
		url: `/pages/list/noticeInfo?type=创建通知`
	},
];

const messageManageApps = [
	// {
	// 	name: 'view_message',
	// 	image: "/static/home/<USER>",
	// 	title: '查看消息',
	// 	url: '/pages/message/view_message'
	// },
	{
		name: 'entirety',
		image: "/static/APP-new/overall_situation_icon.svg",
		title: '整体态势',
		url: '/pages/situationPresentation/entirety'
	},
	{
		name: 'source',
		image: "/static/APP-new/resource_situation_icon.svg",
		title: '资源态势',
		url: '/pages/situationPresentation/source'
	},
	{
		name: 'running',
		image: "/static/APP-new/operation_situation_icon.svg",
		title: '运行态势',
		url: '/pages/situationPresentation/running'
	},
	{
		name: 'user',
		image: "/static/APP-new/user_usage_situation_icon.svg",
		title: '用户使用态势',
		url: '/pages/situationPresentation/user'
	},
	{
		name: 'officialDocument',
		image: "/static/APP-new/document_application_situation_icon.svg",
		title: '公文应用态势',
		url: '/pages/situationPresentation/officialDocument'
	},
];

const allAppMap = {};
let allApps = [...assetManageApps, ...listManageApps, ...messageManageApps];
for (let app of allApps) {
	allAppMap[app.name] = app;
}

export default {
	data() {
		return {
			commonAppNames: [],
			// 用户权限列表
			userPermissions: [],
			// 是否已加载权限
			permissionsLoaded: false,
			// 权限检查定时器
			permissionCheckTimer: null,
			// 权限是否已变更
			permissionChanged: false
		}
	},
	onLoad() {
		console.log(" on load ....");
		this.initCommonAppNames();
		// 加载用户权限
		this.loadUserPermissions();
		// 启动权限变更检查定时器
		this.startPermissionChangeCheck();
	},

	onUnload() {
		// 清除权限检查定时器
		this.stopPermissionChangeCheck();
	},
	computed: {
		defaultCommonApps() {
			return defaultCommonApps;
		},
		// 常用app（已过滤权限）
		commonApps() {
			let commonAppNames = this.commonAppNames;
			let commonApps = [];
			let allAppMap = this.allAppMap;
			for (let commonAppName of commonAppNames) {
				let app;
				if (app = allAppMap[commonAppName]) {
					// 检查是否有权限访问该应用
					if (this.hasPermission(app.name)) {
						commonApps.push(app);
					}
				}
			}
			return commonApps;
		},
		// 告警管理应用（已过滤权限）
		assetManageApps() {
			// 如果权限尚未加载完成，返回空数组
			if (!this.permissionsLoaded && this.userPermissions.length === 0) {
				return [];
			}
			// 过滤出有权限的应用
			return assetManageApps.filter(app => this.hasPermission(app.name));
		},
		// 工单管理应用（已过滤权限）
		listManageApps() {
			// 如果权限尚未加载完成，返回空数组
			if (!this.permissionsLoaded && this.userPermissions.length === 0) {
				return [];
			}
			// 过滤出有权限的应用
			return listManageApps.filter(app => this.hasPermission(app.name));
		},
		// 态势呈现应用（已过滤权限）
		messageManageApps() {
			// 如果权限尚未加载完成，返回空数组
			if (!this.permissionsLoaded && this.userPermissions.length === 0) {
				return [];
			}
			// 过滤出有权限的应用
			return messageManageApps.filter(app => this.hasPermission(app.name));
		},
		// 所有应用映射
		allAppMap() {
			return allAppMap;
		},
		// 是否显示告警管理分类
		showAssetManageCategory() {
			return this.assetManageApps.length > 0;
		},
		// 是否显示工单管理分类
		showListManageCategory() {
			return this.listManageApps.length > 0;
		},
		// 是否显示态势呈现分类
		showMessageManageCategory() {
			return this.messageManageApps.length > 0;
		}
	},
	methods: {
		/**
		 * 模糊匹配函数 - 搜索应用并过滤权限
		 * @param {String} keyword 搜索关键词
		 * @returns {Array} 过滤后的应用列表
		 */
		fuzzySearch(keyword) {
			const searchKey = keyword.trim().toLowerCase();
			if (!searchKey) return []; // 空关键词返回空数组

			// 先过滤权限，再搜索关键词
			return allApps
				.filter(app => this.hasPermission(app.name))
				.filter(app => app.title.toLowerCase().includes(searchKey));
		},

		/**
		 * 初始化常用应用名称列表
		 */
		initCommonAppNames() {
			let commonAppNames = [...this.defaultCommonApps];
			if (typeof uni) {
				let __common_app_names = uni.getStorageSync("__common_app_names");
				if (__common_app_names) {
					commonAppNames = JSON.parse(__common_app_names);
				}
			}
			this.commonAppNames = commonAppNames;
		},

		/**
		 * 持久化常用应用名称到本地存储
		 */
		storageCommonAppNames() {
			uni.setStorageSync("__common_app_names", JSON.stringify(this.commonAppNames));
		},

		/**
		 * 加载用户权限列表
		 * 从缓存或后端API获取用户的权限列表
		 *
		 * @param {Boolean} forceRefresh 是否强制刷新缓存
		 * @returns {Promise} 返回包含权限列表的Promise对象
		 */
		loadUserPermissions(forceRefresh = false) {
			// 检查是否已登录
			const token = uni.getStorageSync('token');
			if (!token) {
				console.log('用户未登录，无法获取权限列表');
				this.permissionsLoaded = true;
				return Promise.resolve({ status: 0, data: [] });
			}

			// 如果权限已变更，强制刷新缓存
			if (this.permissionChanged) {
				forceRefresh = true;
				this.permissionChanged = false;
			}

			// 调用API获取用户权限列表，传入forceRefresh参数
			return getUserPermissions(forceRefresh)
				.then(res => {
					if (res && res.status === 0 && res.data) {
						// 保存用户权限列表
						this.userPermissions = res.data;
						console.log('用户权限列表加载成功:', this.userPermissions);
					} else {
						// API返回错误，使用默认权限
						console.warn('获取用户权限失败，使用默认权限:', res);
						this.userPermissions = [...defaultPermissions];
					}

					// 标记权限已加载完成
					this.permissionsLoaded = true;

					// 返回结果，以便链式调用
					return res;
				})
				.catch(err => {
					// API调用失败，使用默认权限
					console.error('获取用户权限出错，使用默认权限:', err);
					this.userPermissions = [...defaultPermissions];

					// 标记权限已加载完成
					this.permissionsLoaded = true;

					// 抛出错误，以便调用者处理
					throw err;
				});
		},

		/**
		 * 检查用户是否有权限访问指定应用
		 * @param {String} appName 应用名称
		 * @returns {Boolean} 是否有权限
		 */
		hasPermission(appName) {
			// 如果权限列表为空，且权限已加载完成，则默认无权限
			if (this.userPermissions.length === 0 && this.permissionsLoaded) {
				return false;
			}

			// 如果权限列表为空，但权限尚未加载完成，则默认有权限
			if (this.userPermissions.length === 0 && !this.permissionsLoaded) {
				return true;
			}

			// 检查应用是否在权限列表中
			return this.userPermissions.includes(appName);
		},

		/**
		 * 启动权限变更检查定时器
		 * 每隔一定时间检查一次权限是否有变更
		 */
		startPermissionChangeCheck() {
			// 清除已有的定时器
			this.stopPermissionChangeCheck();

			// 创建新的定时器，每10分钟检查一次权限变更
			this.permissionCheckTimer = setInterval(() => {
				this.checkPermissionChanges();
			}, 10 * 60 * 1000); // 10分钟

			// 立即执行一次检查
			this.checkPermissionChanges();
		},

		/**
		 * 停止权限变更检查定时器
		 */
		stopPermissionChangeCheck() {
			if (this.permissionCheckTimer) {
				clearInterval(this.permissionCheckTimer);
				this.permissionCheckTimer = null;
			}
		},

		/**
		 * 检查权限是否有变更
		 * 如果有变更，则重新加载权限并通知用户
		 */
		checkPermissionChanges() {
			// 检查是否已登录
			const token = uni.getStorageSync('token');
			if (!token) {
				return;
			}

			// 调用API检查权限是否有变更
			checkPermissionChanges()
				.then(result => {
					if (result.hasChanges) {
						console.log('用户权限已变更，原因:', result.reason);

						// 标记权限已变更
						this.permissionChanged = true;

						// 清除权限缓存
						clearPermissionsCache();

						// 重新加载权限
						this.loadUserPermissions(true);

						// 通知用户权限已变更
						uni.showToast({
							title: '您的权限已更新',
							icon: 'none',
							duration: 2000
						});
					}
				})
				.catch(err => {
					console.error('检查权限变更出错:', err);
				});
		},

		/**
		 * 刷新当前页面UI
		 * 在权限变更后调用，确保UI显示符合新权限
		 */
		refreshUI() {
			// 通过修改和还原一个不影响显示的属性，触发视图更新
			const temp = this.permissionsLoaded;
			this.permissionsLoaded = false;

			// 使用nextTick确保在DOM更新循环结束后执行
			this.$nextTick(() => {
				this.permissionsLoaded = temp;
			});
		}
	}
}