import $C from './config.js'
//import $C from './common/config.js';

export default {
	data() {
		return {
			timer: null //公共定时器
		};
	},
	// 默认 application/json
	async post(url, param, delay = false) {
		if (delay) {
			uni.showLoading({
				title: '加载中'
			})
			setTimeout(() => {
				uni.hideLoading()
			}, 3000)
		}
		const res = await this.uni_request(url, param, 'post')
		if (delay) {
			setTimeout(() => {
				uni.hideLoading()
			}, 200)
		}
		return res;
	},
	// 使用form
	async postForm(url, param, delay = false) {
		if (delay) {
			uni.showLoading({
				title: '加载中'
			})
			setTimeout(() => {
				uni.hideLoading()
			}, 3000)
		}
		const res = await this.uni_request(url, param, 'post', {
			header: {
				"Content-Type": "application/x-www-form-urlencoded"
			}
		})
		if (delay) {
			setTimeout(() => {
				uni.hideLoading()
			}, 200)
		}
		return res;
	},
	async get(url, param, delay = false) {
		if (delay) {
			uni.showLoading({
				title: '加载中'
			})
			setTimeout(() => {
				uni.hideLoading()
			}, 3000)
		}
		const res = await this.uni_request(url, param, 'get')
		if (delay) {
			setTimeout(() => {
				uni.hideLoading()
			}, 200)
		}
		return res;
	},
	async delete(url, param, delay = false) {
		if (delay) {
			uni.showLoading({
				title: '加载中'
			})
			setTimeout(() => {
				uni.hideLoading()
			}, 3000)
		}
		const res = await this.uni_request(url, param, 'delete')
		if (delay) {
			setTimeout(() => {
				uni.hideLoading()
			}, 200)
		}
		return res;
	},
	async put(url, param) {
		const res = await this.uni_request(url, param, 'put')
		return res;
	},
	async configs(delay = false) {
		if (delay) {
			uni.showLoading({
				title: '加载中'
			})
			setTimeout(() => {
				uni.hideLoading()
			}, 3000)
		}
		let url = 'api/configs'
		let token = uni.getStorageSync('token')
		if (token) {
			let param = {
				token: token
			}
			const res = await this.uni_request(url, param, 'post')
			if (delay) {
				setTimeout(() => {
					uni.hideLoading()
				}, 200)
			}
			uni.setStorageSync('switch', res.data)
			return res.data;
		} else {
			return false;
		}

	},
	uni_request(url, param, method, configs) {
		const that = this;
		let url_ = "";
		// console.log("Come_from:",$C.Come_from);
		// console.log("process.env.NODE_ENV:",process.env.NODE_ENV);
		// let baseUrl = process.env.NODE_ENV === 'development' ? '/api' : $C.api_url;
		if (url.includes("http") || url.includes("https")) {
			url_ = url;
		} else {
			// #ifndef H5
			console.log("手机端", url);
			let replacedUrl = url;
			// if (url.test(/^portal-eam-new\/dap?/)) {
			// 	replacedUrl = url.replace(/^portal-eam-new\/dap?/, '');
			// } else
			let newurl = $C.getBaseUrl();
			if (url?.startsWith('portal-eam-new')) {
				replacedUrl = url.replace(/^portal-eam-new\/?/, '');
			}else{
				newurl= 'http://192.168.1.226:9998'
			}


			console.log(replacedUrl);
			url_ = newurl + replacedUrl;
			// #endif

			// #ifdef H5
			url_ = $C.getBaseUrl() + url;
			// #endif
		}
		console.log("request url:", url_)
		// Content-Type: application/x-www-form-urlencoded application/json
		let {
			header
		} = configs || {};

		return new Promise((cback, reject) => {
			uni.request({
				url: url_,
				data: param,
				sslVerify: false,
				method: method,
				header: {
					// token:uni.getStorageSync("token"),
					...(header || {}),
					"Authorization": uni.getStorageSync("token"),
				},
			}).then(res => {
				console.log(res);
				cback(res.data);
			}).catch(err => {
				uni.showToast({
					title: '请求异常',
					icon: 'none'
				})
				console.log('catch err:', err);
				reject(err);
				// uni.showToast({
				// 	title: '无法连接，请先打开VPN',
				// 	icon: 'none'
				// })
				// uni.showModal({
				// 	title: '网络链接异常',
				// 	content: '请检查网络。并设置正确的VPN。'
				// });
				//this.clearAppUserData();
			})
		})
	},
	clearAppUserData() {
		plus.android.importClass("android.app.ActivityManager");
		var Context = plus.android.importClass("android.content.Context");
		var am = plus.android
			.runtimeMainActivity()
			.getSystemService(Context.ACTIVITY_SERVICE);
		am.clearApplicationUserData();
	},
	//定时刷新token
	refrenceToken() {
		let that = this;
		if (this.timer) {
			this.cleanTimer();
		}
		that.timer = setInterval(function () {
			let token = that.getToken();
			if (token) {
				// 短于公共会话失效的5分钟时间，公共失效时间设置会变，>=24h
				that.get($C.portalContextPathNew + '/login/token/refresh').then(res => {
					if (res.status == "0") {
						uni.setStorageSync('token', res.data);
					} else {

					}
				})
			} else {
				that.cleanTimer();
			}
		}, 60000);
	},
	//清除计时器
	cleanTimer() {
		let that = this;
		clearInterval(that.timer);
		that.timer = null;
	},
	//获取token
	getToken() {
		return uni.getStorageSync("token")
	},
	//判断是否登录
	isLogin() {
		return uni.getStorageSync("token") ? true : false
	},
	//判断登录并跳转到登录页
	checkLoginAndJumpStart() {
		let token = uni.getStorageSync('token');
		if (!this.isLogin()) {
			uni.showToast({
				title: '您尚未登录',
				icon: "none"
			})
			setTimeout(() => {
				uni.redirectTo({
					url: '/pages/login/login_pwd'
				})
			}, 2000);
		}
	},
	//跳转页面，校验登录状态
	href(url, type = 1) {
		if (type == 1) {
			uni.navigateTo({
				url: url
			})
		} else if (type == 2) {
			uni.switchTab({
				url: url
			});
		} else if (type == 3) {
			uni.redirectTo({
				url: url
			})
		}
	}

}