import http from './axios.js'
export default {
	
	// 门户上下文path
	portalContextPath: "/portal-eam/dap",
	portalContextPathNew: "portal-eam-new",
	portalContextPathNewNew: "portal-server",

	//生产环境
	// api_url: "http://***********:8080/rest/",  //生产环境
	// api_url: "https://*************:38292/rest/", //生产环境
	// api_url: "https://36.134.3.251:38292/rest/", //生产环境
	// api_url: "http://*************:8040", // 政数局生产环境
	api_url: "http://*************:11030/", // 本s地生产环境

	//开发环境
	// dev_url: "https://***********:8080/rest/",//开发环境，APP调试
	// dev_url: "https://*************:38292/rest/", //开发环境，APP调试
	dev_url: "http://*************:11030/", //开发环境，APP调试
	dev_url_file: "http://*************:9998/", //开发环境，APP文件调试

	//资源路径，如图片、视频的云端链接
	res_url: "http://localhost:8888",
	static_url: "http://localhost:8888",
	//项目名称
	app_name: "APP",
	shop_name: 'IT',
	//版本
	version: "0.8",
	//来源
	// #ifdef MP-WEIXIN
	Come_from: "miniapp",
	// #endif
	// #ifdef APP-PLUS
	Come_from: "app",
	// #endif
	// #ifdef H5
	Come_from: "h5",
	// #endif
	//接口配置初始化
	setConfig() {
		return http.get("sapi/config/basis").then(res => {
			uni.setStorageSync('config', res.data)
			return res.data
		})
	},
	getBaseUrl() {
		let baseUrl = '';
		if (process.env.NODE_ENV === 'development') {
			// #ifdef H5
			baseUrl = "/api/"; //开发环境：浏览器  走代理，修改manifest.json-源码视图-h5下的url,改后重启服务
			// #endif
			// #ifdef APP-PLUS ||MP
			baseUrl = this.dev_url; //开发环境：直联手机
			// #endif
		} else {
			baseUrl = this.api_url; //生产环境
		}
		return baseUrl;
	}
}
