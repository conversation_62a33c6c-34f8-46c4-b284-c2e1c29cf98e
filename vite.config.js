import {
	defineConfig
} from "vite"
import uni from "@dcloudio/vite-plugin-uni";
 
export default defineConfig({
	plugins: [
		uni()
	],
	server: {
		// http: true, // 是否开启 https
		proxy: {
			// '/api': {
			// 	// target: 'https://36.134.3.251:38292/rest/',
			// 	target: 'http://192.168.1.152:8028/',
			// 	changeOrigin: true,
			// 	secure: false,
			// 	rewrite: path => {
			// 		return path.replace(/^\/api/, '/')
			// 	}
			// },
			'/api//portal-eam': {
				target: 'http://192.168.1.226:11030/',
				changeOrigin: true,
				secure: false,
				rewrite: path => {
					return path.replace(/^\/api\/\/portal-eam/, '/')
				}
			},
			'/api//new': {
				target: 'http://192.168.1.226:11030/',
				changeOrigin: true,
				secure: false,
				rewrite: path => {
					return path.replace(/^\/api\/\/new/, '/')
				}
			},
			'/api/portal-eam-new': {
				target: 'http://192.168.1.226:11030/',
				changeOrigin: true,
				secure: false,
				rewrite: path => {
					return path.replace(/^\/api\/portal-eam-new/, '/')
				}
			},
			// '/api/portal-eam': {
			// 	target: 'http://192.168.1.226:11030/',
			// 	changeOrigin: true,
			// 	secure: false,
			// 	rewrite: path => {
			// 		return path.replace(/^\/api\/\/\/portal-eam/, '/')
			// 	}
			// },
			// http://192.168.1.226:9998
			'/api': {
				target: 'http://192.168.1.226:9998/',
				changeOrigin: true,
				secure: false,
				rewrite: path => {
					return path.replace(/^\/api/, '/')
				}
			},
		}
	}
})